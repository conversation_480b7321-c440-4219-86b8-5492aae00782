var rec_text = ""; // for online rec asr result
var offline_text = ""; // for offline rec asr result
var isfilemode = false; // if it is in file mode
var file_data_array; // array to save file data
var totalsend = 0;

let sampleBuf = new Int16Array();

// 添加录音状态枚举
const RecordingState = {
  STOPPED: "stopped",
  STARTING: "starting",
  RECORDING: "recording",
};

// 替换原来的talking布尔值
let recordingState = RecordingState.STOPPED;

const btnButtonCall = document.querySelector("#btnButtonCall");
const image_call = "url(./images/phone_call.png)"; // 电话呼叫的图片URL
const image_down = "url(./images/phone_down.png)"; // 电话挂断的图片URL

const wsconnecter = new WebSocketConnectMethod({
  msgHandle: getJsonMessage,
  stateHandle: getConnState,
});

let isRec = false;
// 录音; 定义录音对象,wav格式
// const rec = Recorder({
//   type: "pcm",
//   bitRate: 16,
//   sampleRate: 16000,
//   bufferSize: 4096, // 添加较小的缓冲区大小以减少延迟
//   onProcess: recProcess,
// });

var rec = Recorder({
  type: "pcm",
  bitRate: 16,
  sampleRate: 16000,
  // ======= 以下开启浏览器内建 AEC/NS/AGC =======
  audioTrackSet: {
    echoCancellation: true, // 回声消除（AEC） :contentReference[oaicite:1]{index=1}
    noiseSuppression: true, // 噪声抑制（NS） :contentReference[oaicite:2]{index=2}
    autoGainControl: true, // 自动增益控制（AGC） :contentReference[oaicite:3]{index=3}
  },
  bufferSize: 4096,
  onProcess: recProcess,
});

// 在文件顶部添加关键词数组
const TRIGGER_KEYWORDS = ["小幸运", "小。幸运"];

// 添加控制音频静音的函数
function toggleMute(isMuted) {
  // const audioElement = document.getElementById("audio");
  // const videoElement = document.getElementById("video");

  // audioElement.muted = isMuted;
  videoElement.muted = isMuted;

  console.log(`音频和视频已${isMuted ? "静音" : "取消静音"}`);
}

$(document).ready(function () {
  // 页面加载完成后自动执行start()
  // init_stream();
  start();
  startSpeakingCheck(); // 启动说话状态检查

  // 在页面加载时取消音频和视频的静音状态
  toggleMute(false);

  // 设置初始按钮背景图片
  btnButtonCall.style.backgroundImage = image_call;

  // 为"呼叫"按钮添加点击事件监听器
  btnButtonCall.addEventListener("click", function (event) {
    if (btnButtonCall.style.backgroundImage.includes("phone_call")) {
      toggleMute(false); // 取消静音
      show_click_message = false; // 只显示一次点击消息
      btnButtonCall.style.backgroundImage = image_down; // 改变按钮图片为挂断电话
      start_record(); // 开始录音
      
    } else {
      btnButtonCall.style.backgroundImage = image_call; // 改变按钮图片为呼叫电话
      stop(); // 停止录音
      toggleMute(true); // 设置静音
    }
  });
});

function start_record() {
  if (recordingState !== RecordingState.STOPPED) {
    console.log(`录音当前状态: ${recordingState}, 等待1秒后重试`);
    sleep(() => start_record(), 1000);
    return;
  }

  try {
    recordingState = RecordingState.STARTING;
    const startResult = ws_start();
    if (startResult === 1) {
      record();
      console.log("录音开始成功");
    } else {
      console.log("录音启动失败");
      stop();
    }
  } catch (error) {
    console.error("录音启动出错:", error);
    stop();
  }
}

function getJsonMessage(jsonMsg) {
  const data = JSON.parse(jsonMsg.data);
  const rectxt = String(data.text);
  // console.log("--------------------data: " + JSON.stringify(data));
  console.log("--------------------org_text: ",rectxt,data.mode,data.is_final);

  // 检查是否包含任意一个关键词
  const hasKeyword = TRIGGER_KEYWORDS.some((keyword) =>
    rectxt.includes(keyword)
  );
  if (!hasKeyword) return;

  const asrmodel = data.mode;
  const is_final = data.is_final;
  const cleanText = rectxt.replace(/ +/g, "");
  
  // 使用switch语句优化条件判断
  switch (asrmodel) {
    case "2pass-offline":
      offline_text += cleanText + "\n";
      rec_text = offline_text;
      // message_send_with_vision(rectxt);
      performVisionAnalysisWithMessage(rectxt);
      console.log("==================2pass-offline: " + rectxt);
      break;
    case "2pass-online":
      offline_text += cleanText + "\n";
      rec_text = offline_text;
      // message_send_with_vision(rectxt);
      performVisionAnalysisWithMessage(rectxt);
      console.log("==================2pass-online: " + rectxt);
      break;
    default:
      rec_text += rectxt;
  }

  // if (is_final) {
  //   wsconnecter.wsStop();
  // }
}

function getConnState(connState) {
  // console.log('connState------------------------------->',connState);
  if (connState === 0) {
    //on open
    if (isfilemode) {
      start_file_send();
    }
  } else if (connState === 1) {
    //stop();
  } else if (connState === 2) {
    stop();
    // console.log("connecttion error");
    alert(
      "连接地址" +
        document.getElementById("wssip").value +
        "失败,请检查asr地址和端口。或试试界面上手动授权，再连接。"
    );
  }
}

function start_file_send() {
  sampleBuf = new Uint8Array(file_data_array);

  var chunk_size = 960; // for asr chunk_size [5, 10, 5]

  while (sampleBuf.length >= chunk_size) {
    sendBuf = sampleBuf.slice(0, chunk_size);
    totalsend = totalsend + sampleBuf.length;
    sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length);
    wsconnecter.wsSend(sendBuf);
  }

  stop();
}

function sleep(callback, delay) {
  setTimeout(() => {
    callback();
  }, delay);
}

function record() {
  rec.open(
    function () {
      try {
        rec.start();
        recordingState = RecordingState.RECORDING;
        console.log("录音已开始");
      } catch (error) {
        console.error("录音启动失败:", error);
        stop();
      }
    },
    function (error) {
      console.error("录音设备打开失败:", error);
      stop();
    }
  );
}

function ws_start() {
  if (recordingState !== RecordingState.STARTING) {
    console.log(`录音状态错误: ${recordingState}`);
    return 0;
  }

  clear();

  const ret = wsconnecter.wsStart();
  if (ret === 1) {
    isRec = true;
    return 1;
  } else {
    console.log("WebSocket连接失败");
    return 0;
  }
}

function stop() {
  if (recordingState === RecordingState.STOPPED) {
    console.log("录音已经停止");
    return;
  }

  try {
    isRec = false;
    rec.stop();
    console.log("录音已停止");
  } catch (error) {
    console.error("停止录音时出错:", error);
  } finally {
    recordingState = RecordingState.STOPPED;
    isRec = false;
  }
}

function clear() {
  rec_text = "";
  offline_text = "";
}

function recProcess(
  buffer,
  powerLevel,
  bufferDuration,
  bufferSampleRate,
  newBufferIdx,
  asyncEnd
) {
  if (!isRec) return;

  const data_48k = buffer[buffer.length - 1];
  const array_48k = [data_48k];
  const data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;

  // 使用更高效的数组操作
  sampleBuf = new Int16Array([...sampleBuf, ...data_16k]);

  const chunk_size = 960;
  while (sampleBuf.length >= chunk_size) {
    const sendBuf = sampleBuf.slice(0, chunk_size);
    wsconnecter.wsSend(sendBuf);
    sampleBuf = sampleBuf.slice(chunk_size);
  }
}

function getHotwords() {
  var jsonresult = { 阿里巴巴: 20, 钱心怡: 40 };
  return JSON.stringify(jsonresult);
}

function getAsrMode() {
  return "2pass";
}

function getUseITN() {
  return true;
}

async function message_get() {
  var message = $("#message").val();
  
  console.log("Sending: " + message);
  console.log("sessionid: ", document.getElementById("sessionid").value);

  // 获取提交按钮并显示分析状态
  const submitBtn = document.getElementById("btnSubmit");
  const originalText = submitBtn.textContent;
  submitBtn.textContent = "分析中...";
  submitBtn.classList.add("analyzing");
  submitBtn.disabled = true;

  try {
    // 检查摄像头是否启动
    if (
      userStream &&
      userCamera &&
      userCamera.videoWidth &&
      userCamera.videoHeight
    ) {
      // 摄像头已启动，进行视觉识别
      console.log("摄像头已启动，进行视觉识别");
      const visionDescription = await performVisionAnalysisWithMessage(message);
    } else {
      // 摄像头未启动，直接发送消息
      console.log("摄像头未启动，直接发送消息");
      message_send(message);
    }

    // 清空输入框
    $("#message").val("");
  } catch (error) {
    console.error("处理消息时出错:", error);
    message_send(message);
    $("#message").val("");
  } finally {
    // 恢复按钮状态
    submitBtn.textContent = originalText;
    submitBtn.classList.remove("analyzing");
    submitBtn.disabled = false;
  }
}

// 新增函数：带消息参数的视觉识别
async function performVisionAnalysisWithMessage(message) {
  return new Promise(async (resolve, reject) => {
    if (!userStream || !userCamera.videoWidth || !userCamera.videoHeight) {
      message_send(message);
      console.log("摄像头未开启，跳过视觉识别");
      resolve(null);
      return;
    }


    // 使用 TRIGGER_KEYWORDS 数组中的所有关键词进行过滤
    let filteredMessage = message;
    TRIGGER_KEYWORDS.forEach((keyword) => {
      filteredMessage = filteredMessage.replace(new RegExp(keyword, "g"), "");
    });

    addChatMessage(filteredMessage, 'right', false, 'text');

    // 创建canvas捕获当前帧
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    canvas.width = userCamera.videoWidth;
    canvas.height = userCamera.videoHeight;

    // 绘制当前视频帧到canvas
    ctx.drawImage(userCamera, 0, 0, canvas.width, canvas.height);

    // 转换为base64图片数据
    const imageData = canvas.toDataURL("image/jpeg", 0.8);

    fetch("/human", {
      body: JSON.stringify({
        interrupt: true,
        type: "vision",
        image: imageData,
        text: filteredMessage, // 传递用户输入的消息
        sessionid: document.getElementById("sessionid").value,
      }),
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
      // cache: "no-store",
    })
    .then(response => response.json())
    .then(data => {
  
        // console.log('Received chat response:', data);
  
        if (data.code === 0 && data.data) {
  
            if(data.type=='text'){
                addChatMessage(data.data, 'left', false, 'szr');
            }else if(data.type=='video'){
                showMediaInTechPlayerHandler(data);
            }else if(data.type=='music'){
                addBackgroundMusic(data);
            }else{
                addChatMessage(data.data, 'left', false, 'szr');
            }
            resolve(data.data);
        }else{
            addChatMessage('消息发送失败，请重试', 'left', false, 'szr');
            resolve(null);
        }
        
    })
    .catch(error => {
        console.error('请求发生错误:', error);
        // 可以在聊天窗口中添加错误提示
        addChatMessage('网络错误，或数字人未开启，请检查连接', 'left', false, 'szr');
        
        // 解决Promise
        resolve(null);
    });

  });
}

function message_send(message) {
  // 使用 TRIGGER_KEYWORDS 数组中的所有关键词进行过滤
  let filteredMessage = message;
  TRIGGER_KEYWORDS.forEach((keyword) => {
    filteredMessage = filteredMessage.replace(new RegExp(keyword, "g"), "");
  });

  addChatMessage(filteredMessage, 'right', false, 'text');
  fetch("/human", {
    body: JSON.stringify({
      text: filteredMessage,
      type: "chat",
      interrupt: true,
      sessionid: parseInt(document.getElementById("sessionid").value),
    }),
    headers: {
      "Content-Type": "application/json",
    },
    method: "POST",
    cache: "no-store",
  })
  .then(response => response.json())
  .then(data => {

      console.log('Received chat response:', data);

      if (data.code === 0 && data.data) {

          if(data.type=='text'){
              addChatMessage(data.data, 'left', false, 'szr');
          }else if(data.type=='video'){
              showMediaInTechPlayerHandler(data);
          }else if(data.type=='music'){
              addBackgroundMusic(data);
          }else{
              addChatMessage(data.data, 'left', false, 'szr');
          }

      }else{
          addChatMessage('消息发送失败，请重试', 'left', false, 'szr');
      }
      
      
  })
  .catch(error => {
      console.error('请求发生错误:', error);
      // 可以在聊天窗口中添加错误提示
      addChatMessage('网络错误，或数字人未开启，请检查连接', 'left', false, 'szr');
  });
  
}

// 添加检查说话状态的函数
function checkSpeakingStatus() {
  fetch("/is_speaking", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      sessionid: parseInt(document.getElementById("sessionid").value),
    }),
    cache: "no-store",
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.code === 0) {
        console.log("当前说话状态:", data.data);

        // 根据说话状态控制麦克风
        if (data.data === true) {
          // 当说话状态为true时，暂停麦克风录音
          if (recordingState === RecordingState.RECORDING) {
            console.log("说话中，暂停录音");
            isRec = false; // 停止音频数据处理
          }
        } else if (data.data === false) {
          // 当说话状态为false时，恢复麦克风录音
          if (recordingState === RecordingState.RECORDING && !isRec) {
            console.log("已停止说话，恢复录音");
            isRec = true; // 恢复音频数据处理
          }
        }
      }
    })
    .catch((error) => console.error("检查说话状态出错:", error));
}

// 启动定期检查
function startSpeakingCheck() {
  // 每1秒检查一次
  setInterval(checkSpeakingStatus, 1000);
}


// <!-- 拖动功能脚本 -->
// video-box 拖动功能
function makeDraggable(element) {
  let isDragging = false;
  let startX = 0;
  let startY = 0;
  let initialLeft = 0;
  let initialTop = 0;

  // 确保元素具有绝对定位
  element.style.position = 'fixed';
  element.style.cursor = 'move';
  element.style.zIndex = '1000';

  function dragStart(e) {
      // 检查是否点击在不可拖动区域（按钮和输入框）
      const target = e.target;
      if (target.tagName === 'BUTTON' || target.tagName === 'TEXTAREA' || target.tagName === 'INPUT') {
          return;
      }
      
      // 检查是否点击在输入框容器内
      const inputBox = target.closest('.input-box');
      if (inputBox) {
          return;
      }

      // 只有在确认可以拖动时才阻止默认行为
      e.preventDefault();

      isDragging = true;
      element.classList.add('dragging');
      
      // 获取当前元素位置
      const rect = element.getBoundingClientRect();
      initialLeft = rect.left;
      initialTop = rect.top;
      
      if (e.type === "touchstart") {
          startX = e.touches[0].clientX;
          startY = e.touches[0].clientY;
      } else {
          startX = e.clientX;
          startY = e.clientY;
      }
      
      // 阻止文本选择（但不影响输入框）
      document.body.style.userSelect = 'none';
  }

  function dragEnd(e) {
      if (!isDragging) return;
      
      isDragging = false;
      element.classList.remove('dragging');
      
      // 恢复文本选择功能
      document.body.style.userSelect = '';
  }

  function drag(e) {
      if (!isDragging) return;
      
      e.preventDefault();
      
      let currentX, currentY;
      if (e.type === "touchmove") {
          currentX = e.touches[0].clientX;
          currentY = e.touches[0].clientY;
      } else {
          currentX = e.clientX;
          currentY = e.clientY;
      }
      
      // 计算移动距离
      const deltaX = currentX - startX;
      const deltaY = currentY - startY;
      
      // 计算新位置
      let newLeft = initialLeft + deltaX;
      let newTop = initialTop + deltaY;
      
      // 获取元素尺寸
      const rect = element.getBoundingClientRect();
      const elementWidth = rect.width;
      const elementHeight = rect.height;
      
      // 限制在页面边界内
      // 左边界：不能小于0
      if (newLeft < 0) {
          newLeft = 0;
      }
      // 右边界：不能超出屏幕右侧
      if (newLeft + elementWidth > window.innerWidth) {
          newLeft = window.innerWidth - elementWidth;
      }
      // 上边界：不能小于0
      if (newTop < 0) {
          newTop = 0;
      }
      // 下边界：不能超出屏幕底部
      if (newTop + elementHeight > window.innerHeight) {
          newTop = window.innerHeight - elementHeight;
      }
      
      // 设置限制后的位置
      element.style.left = newLeft + 'px';
      element.style.top = newTop + 'px';
  }

  // 添加事件监听器
  element.addEventListener("mousedown", dragStart);
  element.addEventListener("touchstart", dragStart);
  
  document.addEventListener("mousemove", drag, { passive: false });
  document.addEventListener("touchmove", drag, { passive: false });
  
  document.addEventListener("mouseup", dragEnd);
  document.addEventListener("touchend", dragEnd);
  
  // 添加双击回到中心功能
  element.addEventListener("dblclick", function() {
      const rect = element.getBoundingClientRect();
      const centerX = (window.innerWidth - rect.width) / 2;
      const centerY = (window.innerHeight - rect.height) / 2;
      
      element.style.left = centerX + 'px';
      element.style.top = centerY + 'px';
      element.style.transform = 'none';
  });
}

// 视频尺寸适配功能
function adaptVideoBoxSize() {
  const videoElement = document.getElementById('video');
  const videoBox = document.getElementById('videoBox');
  
  // 监听视频元数据加载完成事件
  videoElement.addEventListener('loadedmetadata', function() {
      console.log('视频元数据已加载');
      console.log('视频原始尺寸:', videoElement.videoWidth, 'x', videoElement.videoHeight);
      
      // 获取视频的原始尺寸
      const videoWidth = videoElement.videoWidth;
      const videoHeight = videoElement.videoHeight;
      
      if (videoWidth > 0 && videoHeight > 0) {
          // 设置最大尺寸限制
          const maxWidth = Math.min(window.innerWidth * 0.8, 800);
          const maxHeight = Math.min(window.innerHeight * 0.8, 700);
          
          // 计算缩放比例
          const scaleX = maxWidth / videoWidth;
          const scaleY = maxHeight / videoHeight;
          const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小
          
          // 计算适配后的尺寸
          const adaptedWidth = Math.round(videoWidth * scale);
          const adaptedHeight = Math.round(videoHeight * scale);
          
          // 为输入框预留空间 (实际高度为70px)
          const inputBoxHeight = 70;
          const totalHeight = adaptedHeight + inputBoxHeight;
          
          console.log('适配后的尺寸:', adaptedWidth, 'x', totalHeight);
          
          // 应用新尺寸
          videoBox.style.width = adaptedWidth + 'px';
          videoBox.style.height = totalHeight + 'px';
          
          // 如果还没有设置位置，则居中显示
          if (!videoBox.style.left || !videoBox.style.top) {
              const centerX = (window.innerWidth - adaptedWidth) / 2;
              const centerY = (window.innerHeight - totalHeight) / 2;
              
              videoBox.style.left = Math.max(0, centerX) + 'px';
              videoBox.style.top = Math.max(0, centerY) + 'px';
              
              console.log('video-box位置已调整为居中:', centerX, centerY);
          }
      }
  });
  
  // 监听视频尺寸变化
  videoElement.addEventListener('resize', function() {
      console.log('视频尺寸发生变化，重新适配');
      // 触发重新适配
      if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {
          videoElement.dispatchEvent(new Event('loadedmetadata'));
      }
  });
  
  // 监听窗口大小变化
  window.addEventListener('resize', function() {
      console.log('窗口大小发生变化，重新适配');
      
      // 确保元素保持在新的窗口边界内
      const videoBox = document.getElementById('videoBox');
      if (videoBox && videoBox.style.left && videoBox.style.top) {
          const rect = videoBox.getBoundingClientRect();
          let currentLeft = parseInt(videoBox.style.left);
          let currentTop = parseInt(videoBox.style.top);
          
          // 调整位置确保在新窗口边界内
          if (currentLeft + rect.width > window.innerWidth) {
              currentLeft = window.innerWidth - rect.width;
          }
          if (currentTop + rect.height > window.innerHeight) {
              currentTop = window.innerHeight - rect.height;
          }
          if (currentLeft < 0) currentLeft = 0;
          if (currentTop < 0) currentTop = 0;
          
          videoBox.style.left = currentLeft + 'px';
          videoBox.style.top = currentTop + 'px';
      }
      
      // 触发重新适配
      if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {
          videoElement.dispatchEvent(new Event('loadedmetadata'));
      }
  });
}

// 初始化拖动功能和视频适配功能
// document.addEventListener('DOMContentLoaded', function() {
//   const videoBox = document.getElementById('videoBox');
//   makeDraggable(videoBox);
//   adaptVideoBoxSize();
// });