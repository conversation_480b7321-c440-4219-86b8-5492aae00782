<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数字人展示</title>
    <link rel="stylesheet" href="human.css" />
    <link rel="stylesheet" href="page.css" />
    <script src="./face/face-api.js"></script>
</head>

<body>
    <input type="hidden" id="sessionid" value="0">
    <div class="main-container">

        <div id="media">
            <audio id="audio" autoplay="autoplay"></audio>
            <canvas id="canvas"></canvas>
            <canvas id="canvasOrigin"></canvas>
            <div id="canvas-status" class="canvas-status">按住「空格键」可调整数字人，按「Alt」键可调整背景</div>
        </div>

        <!-- 添加语音识别按钮 -->
        <button id="voice-recognition-button" type="button">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
        </button>

        <!-- 设置按钮 -->
        <button id="settings-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="3"></circle>
                <path
                    d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z">
                </path>
            </svg>
        </button>
        <!-- 显示对话记录的按钮 -->
        <button id="show-chat-modal" style="padding: 16px;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                <circle cx="7" cy="10" r="1"></circle>
                <circle cx="12" cy="10" r="1"></circle>
                <circle cx="17" cy="10" r="1"></circle>
            </svg>
        </button>
        <!-- 测试聊天媒体消息的按钮 -->

        <!-- <button id="test-chat-media">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
        </button> -->

        <!-- 测试媒体请求的按钮 -->
        <!-- <button id="test-human-media" style="display: none;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="16"></line>
                <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
        </button> -->

        <!-- 新增：测试媒体播放框按钮 -->

        <!-- <button id="test-media-player-btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect>
                <line x1="7" y1="2" x2="7" y2="22"></line>
                <line x1="17" y1="2" x2="17" y2="22"></line>
                <line x1="2" y1="12" x2="22" y2="12"></line>
                <line x1="2" y1="7" x2="7" y2="7"></line>
                <line x1="2" y1="17" x2="7" y2="17"></line>
                <line x1="17" y1="17" x2="22" y2="17"></line>
                <line x1="17" y1="7" x2="22" y2="7"></line>
            </svg>
        </button> -->

        <!-- 打开语音识别页面按钮 -->
        <button id="open-asr-page"
            style="display: none; margin: 0 10px; padding: 8px 16px; background-color: #4361ee; color: white; border: none; border-radius: 4px; cursor: pointer;">
            打开语音识别页面
        </button>



        <!-- &lt;!&ndash; 弹窗遮罩层 &ndash;&gt;-->
        <!-- <div id="modal-overlay"></div>-->

        <!-- 对话记录弹窗 -->
        <div id="chat-modal">
            <!--        <div class="chat-modal-header" id="chat-drag-handle">-->
            <!--&lt;!&ndash;            <h2>对话记录</h2>&ndash;&gt;-->
            <!--        </div>-->
            <div class="modal-scroll-container" id="chat-content">
                <!--            <button class="close-button" id="close-chat-modal">&times;</button>-->
                <!--            <div class="chat-item">-->
                <!--                This is a long text that should wrap inside the chat item container. This is a long text that should wrap inside-->
                <!--                the chat item container. This is a long text that should wrap inside the chat item container.-->
                <!--            </div>-->
            </div>
        </div>

        <!-- 设置弹窗 -->
        <div id="settings-modal">
            <div class="modal-header" id="drag-handle">
                <h2>控制面板</h2>
                <button class="close-button" id="close-modal">&times;</button>
            </div>

            <div class="modal-scroll-container">
                <div class="tabs">
                    <button class="tab-button active" data-tab="tab-recognition">对话</button>
                    <button class="tab-button" data-tab="tab-appearance">外观</button>
                    <button class="tab-button" data-tab="tab-position">位置</button>
                    <button class="tab-button" data-tab="tab-music">音乐</button>
                    <!-- <button class="tab-button" data-tab="tab-media">数字媒体</button> -->

                </div>

                <!-- 对话标签页 -->
                <div id="tab-recognition" class="tab-content active">


                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                            视觉感知
                        </div>
                        <div class="vision-container">
                            <video id="userCamera" class="vision-camera" autoplay playsinline muted></video>
                            <div class="vision-controls">
                                <button class="vision-btn vision-btn-start" id="startCamera">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M23 7l-7 5 7 5V7z"></path>
                                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                                    </svg>
                                    开启摄像头
                                </button>
                                <button class="vision-btn vision-btn-stop" id="stopCamera" style="display: none;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M23 7l-7 5 7 5V7z"></path>
                                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                                        <line x1="1" y1="1" x2="23" y2="23"></line>
                                    </svg>
                                    关闭摄像头
                                </button>
                            </div>
                            <div id="vision-status" class="vision-status" style="display: none;">

                            </div>
                        </div>
                    </div>


                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                            语音对话
                        </div>
                        <div class="asr-container">
                            <div class="input-box flex-center">
                                <div class="btn-callphone" id="btnButtonCall"></div>
                                <textarea class="input-text" id="message"
                                    onkeydown="if(event.keyCode==13 && !event.shiftKey){event.preventDefault();message_get();return false;}">你是谁？</textarea>
                                <button id="btnSubmit" type="button" class="input-btn"
                                    onclick="message_get()">提交</button>
                                <input type="hidden" id="sessionid" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                </path>
                            </svg>
                            人脸检测
                        </div>
                        <!-- 人脸检测摄像头（移动到主体中，初始隐藏但不会自动隐藏） -->
                        <div id="face-detection-container"
                            style="display:none; position: fixed; top: 20px; right: 20px; z-index: 1010; background: rgba(0,0,0,0.6); border-radius: 8px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">
                            <video id="face-video" width="320" height="240" autoplay muted
                                style="transform: scaleX(-1);"></video>
                            <canvas id="face-canvas" width="320" height="240"
                                style="position: absolute; top: 0; left: 0; transform: scaleX(-1);"></canvas>
                            <div id="face-status"
                                style="position: absolute; bottom: 10px; left: 10px; color: white; font-size: 14px; background: rgba(0,0,0,0.5); padding: 5px 10px; border-radius: 4px;">
                                等待检测...</div>
                        </div>
                        <div class="switch-container" style="justify-content: space-between; margin-bottom: 20px;">
                            <label for="face-detection-toggle"
                                style="margin: 0; font-size: 14px; color: var(--text-secondary);">启用人脸检测（发送语音前需验证人脸）</label>
                            <label class="switch">
                                <input type="checkbox" id="face-detection-toggle" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="info-box"
                            style="background-color: #e3f2fd; padding: 10px; border-radius: 4px; margin-bottom: 15px; border-left: 3px solid #2196f3;">
                            <p style="margin: 0; font-size: 13px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="16"></line>
                                    <line x1="12" y1="16" x2="12" y2="16"></line>
                                </svg>
                                开启此功能后，发送语音识别结果前系统会自动打开摄像头进行人脸验证，只有检测到人脸后才会发送结果
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 外观设置标签页 -->
                <div id="tab-appearance" class="tab-content">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z" />
                            </svg>
                            背景设置
                        </div>

                        <!-- 背景图片预设 -->
                        <div class="form-group">
                            <label>背景图片预设</label>
                            <div class="bg-presets-container"
                                style="max-height: 125px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px;">

                                <!-- 第一行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/1.jpg">
                                        <img src="static/images/bg/1.jpg" alt="背景1">
                                    </div>
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/2.jpg">
                                        <img src="static/images/bg/2.jpg" alt="背景2">
                                    </div>
                                </div>

                                <!-- 第二行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/3.jpg">
                                        <img src="static/images/bg/3.jpg" alt="背景3">
                                    </div>
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/4.jpg">
                                        <img src="static/images/bg/4.jpg" alt="背景4">
                                    </div>
                                </div>

                                <!-- 第三行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/5.jpg">
                                        <img src="static/images/bg/5.jpg" alt="背景5">
                                    </div>
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/6.jpg">
                                        <img src="static/images/bg/6.jpg" alt="背景6">
                                    </div>
                                </div>

                                <!-- 第四行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/7.jpg">
                                        <img src="static/images/bg/7.jpg" alt="背景7">
                                    </div>
                                    <div class="bg-preset-item" data-bg-url="static/images/bg/8.jpg">
                                        <img src="static/images/bg/8.jpg" alt="背景8">
                                    </div>
                                </div>

                                <!-- 第五行动态 -->
                                <!-- <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-bg-url="static\videos/outup.mp4">
                                        <video src="static\videos/outup.mp4" alt="未来科技竖屏3">
                                    </div>
                                    <div class="bg-preset-item" data-bg-url="static\videos/outup.mp4">
                                        <video src="static\videos/outup.mp4" alt="未来科技竖屏4">
                                    </div>
                                </div> -->
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="backgroundUpload">上传背景图片</label>
                            <div class="file-upload">
                                <label for="backgroundUpload" class="file-upload-label">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                                        <circle cx="8.5" cy="8.5" r="1.5" />
                                        <polyline points="21 15 16 10 5 21" />
                                    </svg>
                                    选择图片文件
                                </label>
                                <input type="file" id="backgroundUpload" accept="image/*">
                            </div>
                        </div>
                    </div>


                    <!-- UI设置 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <circle cx="12" cy="12" r="6"></circle>
                            </svg>
                            UI设置
                        </div>
                        <div class="form-group">
                            <label for="uiAutoHideSwitch">启用自动隐藏UI控件</label>
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="uiAutoHideSwitch" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="uiAutoHideDelay">自动隐藏等待时间（毫秒）</label>
                            <div class="range-container">
                                <input type="range" id="uiAutoHideDelay" min="500" max="15000" value="15000">
                                <input type="number" class="range-value" id="uiAutoHideDelayValue" min="500" max="15000"
                                    value="15000">
                            </div>
                            <div style="font-size:12px;color:#888;margin-top:4px;">范围：0.5秒 ~ 15秒</div>
                        </div>
                    </div>

                    <!-- 添加对话框设置部分 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                                <circle cx="7" cy="10" r="1" />
                                <circle cx="12" cy="10" r="1" />
                                <circle cx="17" cy="10" r="1" />
                            </svg>
                            对话框设置
                        </div>
                        <div class="form-group">
                            <label for="chatOpacitySlider">对话框不透明度</label>
                            <div class="range-container">
                                <input type="range" id="chatOpacitySlider" min="0" max="100" value="65">
                                <input type="number" class="range-value" id="chatOpacityValue" min="0" max="100"
                                    value="65">
                            </div>
                        </div>

                        <!-- 添加清空历史对话选项 -->
                        <div class="form-group" style="margin-top: 15px; display: none;">
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="clear-history-toggle" checked>
                                    <span class="slider"></span>
                                </label>
                                <label for="clear-history-toggle">每次进入页面时清空历史对话</label>
                            </div>
                        </div>

                        <!-- 添加手动清空历史按钮 -->
                        <div class="form-group" style="margin-top: 15px; display: none;">
                            <button id="clear-history-now" class="btn-warning" style="width: 100%;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path
                                        d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2">
                                    </path>
                                </svg>
                                立即清空历史对话
                            </button>
                        </div>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
                                <polyline points="3.27 6.96 12 12.01 20.73 6.96" />
                                <line x1="12" y1="22.08" x2="12" y2="12" />
                            </svg>
                            绿幕设置
                        </div>
                        <div class="form-group">
                            <label for="bgColorPicker">抠像背景颜色</label>
                            <div class="color-preview">
                                <input type="color" id="bgColorPicker" value="#31bc78">
                                <input type="text" class="color-value" id="bgColorValue" value="#31bc78">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="toleranceSlider">绿幕识别容差</label>
                            <div class="range-container">
                                <input type="range" id="toleranceSlider" min="0" max="255" value="40">
                                <!--                            <span class="range-value" id="toleranceValue">50</span>-->
                                <input type="number" class="range-value" id="toleranceValue" min="0" max="255"
                                    value="40">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="toleranceSlider">抗锯齿系数</label>
                            <div class="range-container">
                                <input type="range" id="antialiasingSlider" min="0" max="255" value="125">
                                <!--                            <span class="range-value" id="toleranceValue">50</span>-->
                                <input type="number" class="range-value" id="antialiasingValue" min="0" max="255"
                                    value="125">
                            </div>
                        </div>
                    </div>

                    <!-- 将替换到原代码的尺寸设置部分 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                            </svg>
                            数字人大小设置
                        </div>
                        <div class="form-group">
                            <div style="display: flex; justify-content: space-between;">
                                <div class="switch-container" style="margin-right: 10px;">
                                    <label class="switch">
                                        <input type="checkbox" id="originRatioLockCheckbox">
                                        <span class="slider"></span>
                                    </label>
                                    <label for="originRatioLockCheckbox">锁定原始比例</label>
                                </div>
                                <div class="switch-container">
                                    <label class="switch">
                                        <input type="checkbox" id="ratioLockCheckbox">
                                        <span class="slider"></span>
                                    </label>
                                    <label for="ratioLockCheckbox">锁定当前比例</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="customWidthSlider">数字人宽度</label>
                            <div class="range-container">
                                <input type="range" id="customWidthSlider" min="100" max="1900" value="500">
                                <input type="number" class="range-value" id="customWidthValue" min="100" max="1900"
                                    value="500">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="customHeightSlider">数字人高度</label>
                            <div class="range-container">
                                <input type="range" id="customHeightSlider" min="100" max="1900" value="900">
                                <input type="number" class="range-value" id="customHeightValue" min="100" max="1900"
                                    value="900">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 位置调整标签页 -->
                <div id="tab-position" class="tab-content">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                            数字人位置调整
                        </div>
                        <div class="form-group">
                            <label for="digitalHumanPositionMode">播放媒体时数字人位置模式</label>
                            <select id="digitalHumanPositionMode" class="form-control" style="margin-bottom: 15px;">
                                <option value="0">随机位置</option>
                                <option value="1" selected>始终左下角</option>
                                <option value="2">始终右下角</option>
                                <option value="3">左右交替</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="digitalHumanWidthSlider">缩小后数字人宽度</label>
                            <div class="range-container">
                                <input type="range" id="digitalHumanWidthSlider" min="35" max="650" value="300">
                                <input type="number" class="range-value" id="digitalHumanWidthValue" min="35" max="650"
                                    value="300">
                                <div style="margin-top: 8px; display: flex; justify-content: flex-end;">
                                    <button id="setCurrentWidthButton" class="btn-secondary"
                                        style="padding: 6px 10px; font-size: 12px;">
                                        使用当前宽度
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="xOffsetSlider">水平位置 (X轴)</label>
                            <div class="range-container">
                                <input type="range" id="xOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="xOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="yOffsetSlider">垂直位置 (Y轴)</label>
                            <div class="range-container">
                                <input type="range" id="yOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="yOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="button-group"
                                style="display: flex; flex-wrap: nowrap; width: 100%; align-items: stretch;">
                                <button id="positionLeftBottom" class="btn-secondary"
                                    style="flex: 1; white-space: nowrap; min-width: 0; padding: 8px 4px; text-align: center; margin-right: 6px; display: flex; align-items: center; justify-content: center;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 3px;">
                                        <polyline points="4 14 10 14 10 20"></polyline>
                                    </svg>
                                    左下
                                </button>
                                <div
                                    style="display: flex; flex: 2; margin-right: 6px; height: auto; margin-bottom: 8px;">
                                    <button id="positionCenterMiddle" class="btn-secondary"
                                        style="flex: 1; min-width: 0; padding: 8px 4px; text-align: center; border-top-right-radius: 0; border-bottom-right-radius: 0; border-right: none; margin: 0; display: flex; align-items: center; justify-content: center;">
                                        居中
                                    </button>
                                    <button id="positionCenterBottom" class="btn-secondary"
                                        style="flex: 1; min-width: 0; padding: 8px 4px; text-align: center; border-top-left-radius: 0; border-bottom-left-radius: 0; margin: 0; display: flex; align-items: center; justify-content: center;">
                                        底部
                                    </button>
                                </div>
                                <button id="positionRightBottom" class="btn-secondary"
                                    style="flex: 1; white-space: nowrap; min-width: 0; padding: 8px 4px; text-align: center; display: flex; align-items: center; justify-content: center;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 3px;">
                                        <polyline points="20 14 14 14 14 20"></polyline>
                                    </svg>
                                    右下
                                </button>
                            </div>
                        </div>
                        <div class="info-box"
                            style="background-color: #e3f2fd; padding: 12px; border-radius: 8px; margin-top: 16px;">
                            <p style="margin: 0; font-size: 14px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 6px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                </svg>
                                提示: 您也可以按住空格键后直接用鼠标拖动数字人调整位置以及滑动滚轮调整大小。
                            </p>
                        </div>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                <polyline points="21 15 16 10 5 21"></polyline>
                            </svg>
                            背景位置调整
                        </div>
                        <div class="form-group">
                            <label for="bgXOffsetSlider">背景水平位置</label>
                            <div class="range-container">
                                <input type="range" id="bgXOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="bgXOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="bgYOffsetSlider">背景垂直位置</label>
                            <div class="range-container">
                                <input type="range" id="bgYOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="bgYOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="bgScaleSlider">背景缩放比例</label>
                            <div class="range-container">
                                <input type="range" id="bgScaleSlider" min="50" max="200" value="100">
                                <input type="number" class="range-value" id="bgScaleValue" min="50" max="200"
                                    value="100">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="button-group">
                                <button id="bgFitWidth" class="btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <line x1="3" y1="12" x2="21" y2="12"></line>
                                        <polyline points="3 6 3 18"></polyline>
                                        <polyline points="21 6 21 18"></polyline>
                                    </svg>
                                    宽度铺满
                                </button>
                                <button id="bgFitHeight" class="btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <line x1="12" y1="3" x2="12" y2="21"></line>
                                        <polyline points="6 3 18 3"></polyline>
                                        <polyline points="6 21 18 21"></polyline>
                                    </svg>
                                    高度铺满
                                </button>
                            </div>
                        </div>
                        <div class="info-box"
                            style="background-color: #e3f2fd; padding: 12px; border-radius: 8px; margin-top: 16px;">
                            <p style="margin: 0; font-size: 14px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 6px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                </svg>
                                提示: 您也可以按住Alt键后直接用鼠标拖动背景调整位置以及滑动滚轮调整背景大小。
                            </p>
                        </div>
                    </div>

                    <!-- 新增对话框位置调整部分 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                            </svg>
                            对话框位置调整
                        </div>
                        <div class="form-group">
                            <label for="chatXPositionSlider">对话框水平位置</label>
                            <div class="range-container">
                                <input type="range" id="chatXPositionSlider" min="0" max="100" value="50">
                                <input type="number" class="range-value" id="chatXPositionValue" min="0" max="100"
                                    value="50">
                                <span class="unit">%</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="chatYPositionSlider">对话框垂直位置</label>
                            <div class="range-container">
                                <input type="range" id="chatYPositionSlider" min="0" max="100" value="80">
                                <input type="number" class="range-value" id="chatYPositionValue" min="0" max="100"
                                    value="80">
                                <span class="unit">%</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="button-group"
                                style="display: flex; flex-wrap: nowrap; width: 100%; align-items: stretch;">
                                <button id="chatPosLeftTop" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px; margin-right: 6px;">
                                    左上
                                </button>
                                <button id="chatPosCenter" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px; margin-right: 6px;">
                                    居中
                                </button>
                                <button id="chatPosRightBottom" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px;">
                                    右下
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="button-group"
                                style="display: flex; flex-wrap: nowrap; width: 100%; align-items: stretch;">
                                <button id="chatPosBottom" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px; margin-right: 6px;">
                                    左侧居中
                                </button>
                                <button id="chatPosRight" class="btn-secondary" style="flex: 1; padding: 8px 4px;">
                                    右侧居中
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="chatWidthSlider">对话框宽度</label>
                            <div class="range-container">
                                <input type="range" id="chatWidthSlider" min="20" max="100" value="40">
                                <input type="number" class="range-value" id="chatWidthValue" min="20" max="100"
                                    value="40">
                                <span class="unit">%</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="chatHeightSlider">对话框高度</label>
                            <div class="range-container">
                                <input type="range" id="chatHeightSlider" min="20" max="100" value="60">
                                <input type="number" class="range-value" id="chatHeightValue" min="20" max="100"
                                    value="60">
                                <span class="unit">%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 音乐播放器标签页 -->
                <div id="tab-music" class="tab-content">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M9 18V5l12-2v13"></path>
                                <circle cx="6" cy="18" r="3"></circle>
                                <circle cx="18" cy="16" r="3"></circle>
                            </svg>
                            本地音乐播放器
                        </div>

                        <!-- 上传音乐文件和播放列表 -->
                        <div class="music-player-container" style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <!-- 文件选择和列表展示 -->
                            <div class="file-input-section" style="flex: 1; min-width: 250px;">
                                <div class="file-upload" style="margin-bottom: 10px;">
                                    <label for="music-file-input" class="file-upload-label">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="17 8 12 3 7 8"></polyline>
                                            <line x1="12" y1="3" x2="12" y2="15"></line>
                                        </svg>
                                        选择音乐文件 ( 可多选 )
                                    </label>
                                    <input type="file" id="music-file-input" accept="audio/*" multiple>
                                </div>

                                <div class="playlist-container" style="margin-bottom: 15px;">
                                    <div style="font-weight: 500; margin-bottom: 5px;">播放列表</div>
                                    <!-- 新的播放列表UI，使用div和ul/li代替select/option -->
                                    <div class="custom-playlist-wrapper"
                                        style="border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden; max-height: 200px; position: relative;">
                                        <!-- 滚动容器 -->
                                        <div class="custom-playlist-scroll"
                                            style="overflow-y: auto; max-height: 200px; padding: 5px 0;">
                                            <!-- 实际播放列表 -->
                                            <ul id="music-playlist-ul" class="custom-playlist"
                                                style="list-style: none; margin: 0; padding: 0;">
                                                <!-- 音乐项目将在这里动态生成 -->
                                                <li class="playlist-empty-message"
                                                    style="padding: 10px; text-align: center; color: var(--text-secondary);">
                                                    播放列表为空</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <!-- 隐藏的原始select元素，用于兼容现有JS代码 -->
                                    <select id="music-playlist" style="display: none;" hidden>
                                        <!-- 音乐列表会在这里动态生成 -->
                                    </select>
                                    <!-- 当前播放歌曲信息 -->
                                    <div id="now-playing-info"
                                        style="margin: 10px 0; font-size: 13px; color: var(--text-secondary); white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                        未播放任何音乐
                                    </div>
                                    <div class="playlist-controls"
                                        style="display: flex; justify-content: center; gap: 8px; margin-top: 8px;">
                                        <button class="btn-small playlist-control-btn" id="play-selected"
                                            style="flex: 1; background-color: #3a56d4;">
                                            <span class="btn-text-normal">播放选中</span>
                                            <span class="btn-text-short">播放</span>
                                        </button>
                                        <button class="btn-small playlist-control-btn" id="remove-selected"
                                            style="flex: 1; background-color: #e18709;">
                                            <span class="btn-text-normal">移除选中</span>
                                            <span class="btn-text-short">移除</span>
                                        </button>
                                        <button class="btn-small playlist-control-btn" id="clear-playlist"
                                            style="flex: 1; background-color: #d5081c;">
                                            <span class="btn-text-normal">清空列表</span>
                                            <span class="btn-text-short">清空</span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 播放器控制区 -->
                            <div class="player-controls-section" style="flex: 1; min-width: 250px;">

                                <!-- 播放进度条 -->
                                <div class="progress-container" style="margin-bottom: 15px;">
                                    <div
                                        style="display: flex; justify-content: space-between; font-size: 12px; margin-bottom: 3px;">
                                        <span id="current-time">00:00</span>
                                        <span id="total-time">00:00</span>
                                    </div>
                                    <div class="progress-bar-container"
                                        style="position: relative; height: 6px; background: #e0e0e0; border-radius: 3px;">
                                        <div id="music-progress-bar"
                                            style="position: absolute; height: 100%; background: var(--primary-color); width: 0; border-radius: 3px;">
                                        </div>
                                        <input type="range" id="music-progress-slider"
                                            style="position: absolute; width: 100%; height: 100%; opacity: 0; cursor: pointer;"
                                            min="0" max="100" value="0">
                                    </div>
                                </div>

                                <!-- 音量控制 - 移到进度条下方 -->
                                <div class="volume-control"
                                    style="display: flex; align-items: center; gap: 8px; margin-bottom: 15px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                                        <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                                        <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                                    </svg>
                                    <input type="range" id="volume-slider" min="0" max="100" value="80"
                                        style="flex-grow: 1;">
                                    <span id="volume-value">80%</span>
                                </div>

                                <!-- 播放控制按钮 - 重新排列布局 -->
                                <div class="playback-controls"
                                    style="display: flex; justify-content: center; align-items: center; gap: 15px; margin-bottom: 15px;">
                                    <button id="prev-track" class="circle-button"
                                        style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: var(--primary-color); border: none; color: white; padding: 0;">
                                        <!-- 图标将通过JS动态设置 -->
                                    </button>
                                    <button id="play-pause" class="circle-button"
                                        style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: var(--primary-color); border: none; color: white; padding: 0;">
                                        <!-- 图标将通过JS动态设置 -->
                                    </button>
                                    <button id="next-track" class="circle-button"
                                        style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: var(--primary-color); border: none; color: white; padding: 0;">
                                        <!-- 图标将通过JS动态设置 -->
                                    </button>
                                </div>

                                <!-- 播放模式设置 -->
                                <div class="playback-settings">
                                    <div class="setting-item" style="margin-bottom: 10px;">
                                        <label for="loop-mode">循环模式:</label>
                                        <div class="custom-select-wrapper"
                                            style="position: relative; display: inline-block; width: calc(100% - 80px);">
                                            <select id="loop-mode"
                                                style="appearance: none; -webkit-appearance: none; -moz-appearance: none; width: 100%; border-radius: 4px; border: 1px solid var(--border-color); padding: 8px 30px 8px 10px; background-color: var(--bg-color); cursor: pointer;">
                                                <option value="list">列表循环</option>
                                                <option value="single">单曲循环</option>
                                                <option value="no-loop">不循环</option>
                                                <option value="random">随机播放</option>
                                            </select>
                                            <div class="select-arrow"
                                                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="6 9 12 15 18 9"></polyline>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="setting-item"
                                        style="margin-bottom: 10px; display: flex; align-items: center;">
                                        <label for="loop-count" style="margin-right: 10px;">循环次数:</label>
                                        <input type="number" id="loop-count" min="0" max="100" value="1"
                                            style="width: 60px; border-radius: 4px; border: 1px solid var(--border-color); padding: 5px;">
                                        <span
                                            style="margin-left: 5px; color: var(--text-secondary); font-size: 12px;">(0表示无限循环)</span>
                                    </div>

                                    <div class="setting-item"
                                        style="margin-bottom: 10px; display: flex; align-items: center;">
                                        <label for="loop-interval" style="margin-right: 10px;">循环间隔(秒):</label>
                                        <input type="number" id="loop-interval" min="0" max="60" value="2"
                                            style="width: 60px; border-radius: 4px; border: 1px solid var(--border-color); padding: 5px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 隐藏的音频元素用于实际播放 -->
                        <audio id="music-player" style="display:none;"></audio>
                    </div>
                </div>

                <!-- <div id="tab-media" class="tab-content">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                            媒体框控制
                        </div>
                        <div class="button-group">
                            <button id="toggle-media-frame" class="control-button" style="position: static; width: auto; height: auto; padding: 10px 20px; margin-bottom: 10px; display: inline-flex; align-items: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" style="margin-right: 8px;">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                </svg>
                                打开媒体框
                            </button>
                            <button id="close-media-frame" class="control-button" style="position: static; width: auto; height: auto; padding: 10px 20px; margin-bottom: 10px; display: inline-flex; align-items: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" style="margin-right: 8px;">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                </svg>
                                仅关闭媒体框
                            </button>
                        </div>
                    </div>
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                                <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path>
                                <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path>
                            </svg>
                            动画效果
                        </div>
                        <div class="button-group" style="display: flex; flex-wrap: wrap; gap: 8px;">
                            <button class="animation-button active" data-animation="scale">
                                缩放动画
                            </button>
                            <button class="animation-button" data-animation="rotate">
                                旋转动画
                            </button>
                            <button class="animation-button" data-animation="slide">
                                滑动动画
                            </button>
                            <button class="animation-button" data-animation="flip">
                                翻转动画
                            </button>
                            <button class="animation-button" data-animation="bounce">
                                弹性动画
                            </button>
                            <button class="animation-button" data-animation="fade">
                                淡入动画
                            </button>
                        </div>
                    </div>
                </div> -->


            </div>
        </div>
    </div>
    <script src="config.js"></script>

    <script type="text/javascript" src="http://cdn.sockjs.org/sockjs-0.3.4.js"></script>
    <script type="text/javascript" src="https://code.jquery.com/jquery-2.1.1.min.js"></script>

    <script type="text/javascript" src="asr/recorder-core.js"></script>
    <script type="text/javascript" src="asr/wav.js"></script>
    <script type="text/javascript" src="asr/pcm.js"></script>
    <script type="text/javascript" src="asr/wsconnecter.js"></script>

    <script type="text/javascript" src="human.js"></script>
    <script type="text/javascript" src="utils.js" charset="utf-8"></script>


    <script src="music-player.js"></script>

    <script type="text/javascript" src="page.js" charset="utf-8"></script>


    <script type="text/javascript" src="camera-vision.js"></script>

    <!-- <script defer="defer" src="bundle.js"></script> -->

</body>

</html>