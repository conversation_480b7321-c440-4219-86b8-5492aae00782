// 初始化加载动画
async function initLoadingAnimation() {
    try {
        // 动态导入加载动画组件
        const { default: LoadingAnimation } = await import('./loading-animation-component.js');
        
        // 创建加载动画实例
        const loadingAnimation = new LoadingAnimation({
            loadingText: '数字人系统初始化中',
            autoStart: true,
            loadDuration: 500,
            duration: 750,
            onComplete: () => {
                console.log('加载动画完成，页面内容已完全显示');
            }
        });

        // 将加载动画实例绑定到window对象，以便其他地方可以访问
        window.loadingAnimation = loadingAnimation;
    } catch (error) {
        console.error('加载动画组件加载失败:', error);
    }
}

// 初始化Face-API模型路径
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 初始化加载动画
        // await initLoadingAnimation();
        
        // 设置模型加载路径，使用jsDelivr CDN加速
        const MODEL_URL = './moduls/';

        // 加载人脸检测模型（只加载最基础的模型即可）
        await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);
        console.log('人脸检测模型加载成功');

        // 将faceapi对象绑定到window，方便全局访问
        window.faceApiLoaded = true;
    } catch (error) {
        console.error('人脸检测模型加载失败:', error);
    }
});

// 全局变量
let faceDetectionActive = false;
let faceDetectionInterval = null;
let faceDetected = false;
let faceDetectionQueue = []; // 存储等待人脸检测的消息队列
let videoStream = null;

// 初始化摄像头和人脸检测
async function initFaceDetection() {


    if (!window.faceApiLoaded) {
        console.error('人脸检测模型尚未加载完成');
        return false;
    }

    // 获取DOM元素
    const video = document.getElementById('face-video');
    const container = document.getElementById('face-detection-container');
    const faceStatus = document.getElementById('face-status');

    try {
        // 请求摄像头权限
        videoStream = await navigator.mediaDevices.getUserMedia({
            video: {
                width: 320,
                height: 240,
                facingMode: 'user'
            }
        });

        // 设置视频源
        video.srcObject = videoStream;

        // 显示容器，永久不显示人脸检测框
        // container.style.display = 'block';
        faceStatus.textContent = '正在检测人脸...';

        // 设置人脸检测标志
        faceDetectionActive = true;

        // 启动人脸检测循环
        startFaceDetection();

        return true;
    } catch (error) {
        console.error('获取摄像头失败:', error);
        faceStatus.textContent = '摄像头访问失败';
        return false;
    }
}

// <!-- 添加人脸检测相关的JavaScript -->

// 停止人脸检测
function stopFaceDetection() {
    // 清除检测循环
    if (faceDetectionInterval) {
        clearInterval(faceDetectionInterval);
        faceDetectionInterval = null;
    }

    // 停止视频流
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }

    // 隐藏容器
    const container = document.getElementById('face-detection-container');
    if (container) {
        container.style.display = 'none';
    }

    // 重置状态
    faceDetectionActive = false;
    faceDetected = false;
}

// 启动人脸检测循环
function startFaceDetection() {
    const video = document.getElementById('face-video');
    const canvas = document.getElementById('face-canvas');
    const faceStatus = document.getElementById('face-status');
    const displaySize = { width: video.width, height: video.height };

    // 设置canvas尺寸
    faceapi.matchDimensions(canvas, displaySize);

    // 启动检测循环
    faceDetectionInterval = setInterval(async () => {
        if (!video || !faceDetectionActive) return;

        try {
            // 使用TinyFaceDetector进行人脸检测
            const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions());

            // 绘制检测结果
            const context = canvas.getContext('2d');
            context.clearRect(0, 0, canvas.width, canvas.height);

            // 可视化人脸检测结果
            if (detections.length > 0) {
                const resizedDetections = faceapi.resizeResults(detections, displaySize);
                faceapi.draw.drawDetections(canvas, resizedDetections);

                // 标记人脸已检测到
                if (!faceDetected) {
                    faceDetected = true;
                    faceStatus.textContent = '人脸检测成功!';
                    faceStatus.style.backgroundColor = 'rgba(46, 196, 182, 0.7)';

                    // 处理队列中的消息
                    processFaceDetectionQueue();
                }
            } else {
                faceDetected = false;
                faceStatus.textContent = '未检测到人脸';
                faceStatus.style.backgroundColor = 'rgba(255, 159, 28, 0.7)';
            }
        } catch (error) {
            console.error('人脸检测错误:', error);
        }
    }, 75); // 每100毫秒检测一次

    // 设置自动关闭定时器，3000毫秒后停止人脸检测
    setTimeout(() => {
        // 如果还没检测到人脸，显示超时信息
        if (!faceDetected) {
            faceStatus.textContent = '检测超时，请重试';
            faceStatus.style.backgroundColor = 'rgba(247, 37, 133, 0.7)';
        }

        // 1秒后自动关闭检测
        setTimeout(() => {
            console.log('人脸检测时间到，自动停止检测');
            stopFaceDetection();
        }, 1000);
    }, 2000);
}

// 添加消息到人脸检测队列
function addToFaceDetectionQueue(message) {
    // 将消息添加到队列
    faceDetectionQueue.push(message);

    // 获取最后一个队列消息
    const lastMessage = faceDetectionQueue[faceDetectionQueue.length - 1];
    // 将最后一个消息覆盖整个队列
    faceDetectionQueue = [lastMessage];
    // 如果人脸检测尚未初始化，则初始化它
    if (!faceDetectionActive) {
        initFaceDetection();
    }
    // 如果已经检测到人脸，则处理队列（队列最后一个消息）
    else if (faceDetected) {
        processFaceDetectionQueue();
    }
}

// 处理人脸检测队列
async function processFaceDetectionQueue() {
    if (faceDetectionQueue.length === 0 || !faceDetected) return;

    // 获取sessionId
    const sessionId = parseInt(window.parent.document.getElementById('sessionid').value);

    // 处理队列中的所有消息
    for (const message of faceDetectionQueue) {
        try {

            const stopMusicResult = window.parent.checkAndStopMusic(message, ['暂停音乐', '音乐暂停', '关闭音乐', '音乐关闭']);
            const playMusicResult = window.parent.checkAndPlayMusic(message, ['播放音乐', '音乐继续', '继续播放', '开启音乐', '音乐开启']);
            if (stopMusicResult || playMusicResult) {
                console.log('检测到音乐控制命令，仅处理音乐控制，不发送到对话系统');
                return;
            }
            // 发送消息到后端
            const response = await fetch(`http://${window.parent.host}/human`, {
                body: JSON.stringify({
                    text: message,
                    type: window.messageType,
                    interrupt: window.messageInterrupt,
                    sessionid: sessionId,
                }),
                headers: {
                    'Content-Type': 'application/json'
                },
                method: 'POST'
            });

            // 处理响应
            if (!response.ok) {
                console.error('后端响应错误:', response.status);
                const info_div = document.getElementById('info_div');
                if (info_div) {
                    info_div.innerHTML = '<span style="color:#f72585">❌ 发送到后端失败，状态码: ' + response.status + '</span>';
                }
            } else {
                console.log('识别文本发送成功:', message);
                // 如果是在iframe中，通知父窗口添加聊天消息
            }
        } catch (error) {
            console.error('发送消息错误:', error);
        }
    }

    // 清空队列
    faceDetectionQueue = [];

    stopFaceDetection();
}

// 将函数暴露给全局作用域，以便iframe可以调用
window.addToFaceDetectionQueue = addToFaceDetectionQueue;
window.initFaceDetection = initFaceDetection;
window.stopFaceDetection = stopFaceDetection;

//=================================================================
// 确保全局变量可用于宽高比逻辑
window.videoRatioWidth = null;
window.videoRatioHeight = null;

// 人脸检测控制变量
window.useFaceDetection = true;

// 消息控制变量
window.messageInterrupt = true;
window.messageType = 'chat';

// 扩展window对象上的getter，在client.js中设置的值会更新到window对象
Object.defineProperty(window, 'videoRatioWidth', {
    get: function () { return window._videoRatioWidth; },
    set: function (val) {
        window._videoRatioWidth = val;
        console.log("设置window.videoRatioWidth:", val);
    }
});

Object.defineProperty(window, 'videoRatioHeight', {
    get: function () { return window._videoRatioHeight; },
    set: function (val) {
        window._videoRatioHeight = val;
        console.log("设置window.videoRatioHeight:", val);
    }
});

// 无人播放文件夹功能
document.addEventListener('DOMContentLoaded', function () {
    const browseBtn = document.getElementById('browse-folder-btn');
    const pathInput = document.getElementById('nb-live-path');
    const submitBtn = document.getElementById('submit-nb-path');
    const successToast = document.getElementById('nb-success-toast');
    const toastMessage = document.getElementById('nb-toast-message');
    const faceDetectionToggle = document.getElementById('face-detection-toggle');
    const interruptToggle = document.getElementById('interrupt-toggle');
    const messageTypeSelect = document.getElementById('message-type-select');

    // 从localStorage加载打断和消息类型设置
    try {
        // 加载打断设置
        const savedInterrupt = localStorage.getItem('messageInterrupt');
        if (savedInterrupt !== null) {
            window.messageInterrupt = savedInterrupt === 'true';
            if (interruptToggle) {
                interruptToggle.checked = window.messageInterrupt;
            }
        }

        // 加载消息类型设置
        const savedMessageType = localStorage.getItem('messageType');
        if (savedMessageType && messageTypeSelect) {
            window.messageType = savedMessageType;
            messageTypeSelect.value = window.messageType;
        }
    } catch (e) {
        console.error('加载消息控制设置失败:', e);
    }

    // 监听打断开关变化
    if (interruptToggle) {
        interruptToggle.addEventListener('change', function () {
            window.messageInterrupt = this.checked;
            try {
                localStorage.setItem('messageInterrupt', window.messageInterrupt);
                console.log(`消息打断已${window.messageInterrupt ? '开启' : '关闭'}`);
            } catch (e) {
                console.error('保存消息打断设置失败:', e);
            }
        });
    }

    // 监听消息类型选择变化
    if (messageTypeSelect) {
        messageTypeSelect.addEventListener('change', function () {
            window.messageType = this.value;
            try {
                localStorage.setItem('messageType', window.messageType);
                console.log(`消息类型已设置为: ${window.messageType}`);
            } catch (e) {
                console.error('保存消息类型设置失败:', e);
            }
        });
    }

    // 从localStorage加载人脸检测设置
    try {
        const savedFaceDetection = localStorage.getItem('useFaceDetection');
        if (savedFaceDetection !== null) {
            window.useFaceDetection = savedFaceDetection === 'true';
            if (faceDetectionToggle) {
                faceDetectionToggle.checked = window.useFaceDetection;
            }
        }
    } catch (e) {
        console.error('加载人脸检测设置失败:', e);
    }

    // 监听人脸检测开关状态变化
    if (faceDetectionToggle) {
        faceDetectionToggle.addEventListener('change', function () {
            window.useFaceDetection = this.checked;
            try {
                localStorage.setItem('useFaceDetection', window.useFaceDetection);
                showToast(`人脸检测已${window.useFaceDetection ? '开启' : '关闭'}`, window.useFaceDetection ? 'success' : 'info');
                console.log(`人脸检测已${window.useFaceDetection ? '开启' : '关闭'}`);
            } catch (e) {
                console.error('保存人脸检测设置失败:', e);
            }
        });
    }

    // 获取数字人位置模式选择框
    const digitalHumanPositionModeSelect = document.getElementById('digitalHumanPositionMode');

    // 从localStorage加载数字人位置模式设置
    try {
        const savedPositionMode = localStorage.getItem('digitalHumanPositionMode');
        if (savedPositionMode !== null) {
            window.digitalHumanPositionMode = parseInt(savedPositionMode);
            if (digitalHumanPositionModeSelect) {
                digitalHumanPositionModeSelect.value = window.digitalHumanPositionMode.toString();
                console.log(`已加载数字人位置模式: ${window.digitalHumanPositionMode}`);
            }
        }
    } catch (e) {
        console.error('加载数字人位置模式设置失败:', e);
    }

    // 监听数字人位置模式选择变化
    if (digitalHumanPositionModeSelect) {
        digitalHumanPositionModeSelect.addEventListener('change', function () {
            window.digitalHumanPositionMode = parseInt(this.value);
            try {
                localStorage.setItem('digitalHumanPositionMode', window.digitalHumanPositionMode);
                console.log(`数字人位置模式已设置为: ${window.digitalHumanPositionMode}`);
            } catch (e) {
                console.error('保存数字人位置模式设置失败:', e);
            }
        });
    }

    // 数字人缩小宽度控制
    const digitalHumanWidthSlider = document.getElementById('digitalHumanWidthSlider');
    const digitalHumanWidthValue = document.getElementById('digitalHumanWidthValue');
    const setCurrentWidthButton = document.getElementById('setCurrentWidthButton');

    // 初始化数字人宽度控制器
    if (digitalHumanWidthSlider && digitalHumanWidthValue) {
        // 从localStorage加载保存的宽度
        const savedWidth = localStorage.getItem('digitalHumanWidth');
        if (savedWidth) {
            const width = parseInt(savedWidth);
            window.digitalHumanWidth = width;
            digitalHumanWidthSlider.value = width;
            digitalHumanWidthValue.value = width;
            console.log('从localStorage加载数字人宽度:', width);
        } else {
            // 使用默认值
            digitalHumanWidthSlider.value = window.digitalHumanWidth;
            digitalHumanWidthValue.value = window.digitalHumanWidth;
        }

        // 监听滑动条变化
        digitalHumanWidthSlider.addEventListener('input', function () {
            const width = parseInt(this.value);
            digitalHumanWidthValue.value = width;
            window.digitalHumanWidth = width;
            localStorage.setItem('digitalHumanWidth', width);
            console.log('数字人宽度已设置为:', width);
        });

        // 监听输入框变化
        digitalHumanWidthValue.addEventListener('change', function () {
            let width = parseInt(this.value);

            // 限制在有效范围内
            if (width < 35) width = 35;
            if (width > 650) width = 650;

            this.value = width;
            digitalHumanWidthSlider.value = width;
            window.digitalHumanWidth = width;
            localStorage.setItem('digitalHumanWidth', width);
            console.log('数字人宽度已设置为:', width);
        });
    }

    // 应用当前宽度按钮
    if (setCurrentWidthButton) {
        setCurrentWidthButton.addEventListener('click', function () {
            const canvas = document.getElementById('canvas');
            if (canvas) {
                // 获取当前canvas的实际宽度
                const currentWidth = canvas.offsetWidth;

                // 限制在有效范围内
                let width = currentWidth;
                if (width < 35) width = 35;
                if (width > 650) width = 650;

                // 更新UI和全局变量
                digitalHumanWidthSlider.value = width;
                digitalHumanWidthValue.value = width;
                window.digitalHumanWidth = width;
                localStorage.setItem('digitalHumanWidth', width);

                console.log('已应用当前数字人宽度:', width);

                // 显示成功提示
                if (typeof showToast === 'function') {
                    showToast('已应用当前数字人宽度', 'success');
                }
            }
        });
    }

    if (browseBtn && pathInput && submitBtn) {
        // 点击选择文件夹按钮的处理
        browseBtn.addEventListener('click', async function () {
            // 检查是否支持现代文件系统访问API
            if ('showDirectoryPicker' in window) {
                try {
                    // 警告用户关于浏览器限制
                    showToast("由于浏览器安全限制，只能获取文件夹名称而非完整路径，请在选择后手动补全", "warning", 6000);

                    // 使用现代API选择文件夹
                    const dirHandle = await window.showDirectoryPicker();

                    // 获取文件夹名称
                    const folderName = dirHandle.name;

                    // 判断操作系统类型，提供默认路径格式
                    const isWindows = navigator.platform.indexOf('Win') > -1;

                    if (isWindows) {
                        // Windows路径格式，需要用户补全驱动器和前面的路径
                        const currentPath = pathInput.value.trim();

                        // 如果用户已有输入，尝试保留前面部分
                        if (currentPath) {
                            // 检查是否是有效的Windows路径格式
                            if (/^[A-Z]:\\/.test(currentPath)) {
                                // 如果是完整路径，替换最后一级文件夹
                                pathInput.value = currentPath.replace(/[^\\]*$/, folderName);
                            } else {
                                // 不是完整路径，提示用户
                                pathInput.value = `C:\\路径\\到\\${folderName}`;
                                showToast("请替换为实际完整路径", "warning");
                            }
                        } else {
                            // 用户没有输入，提供示例
                            pathInput.value = `C:\\路径\\到\\${folderName}`;
                            showToast("请替换为实际完整路径", "warning");
                        }
                    } else {
                        // Unix/Linux/Mac路径格式
                        const currentPath = pathInput.value.trim();

                        if (currentPath) {
                            // 如果用户已有输入，尝试保留前面部分
                            if (currentPath.startsWith('/')) {
                                // 如果是完整路径，替换最后一级文件夹
                                pathInput.value = currentPath.replace(/[^\/]*$/, folderName);
                            } else {
                                // 不是完整路径，提示用户
                                pathInput.value = `/路径/到/${folderName}`;
                                showToast("请替换为实际完整路径", "warning");
                            }
                        } else {
                            // 用户没有输入，提供示例
                            pathInput.value = `/路径/到/${folderName}`;
                            showToast("请替换为实际完整路径", "warning");
                        }
                    }

                    // 将光标放在输入框内
                    pathInput.focus();
                    // 选中路径部分以便用户修改
                    if (isWindows) {
                        const pathParts = pathInput.value.split('\\');
                        if (pathParts.length > 2) {
                            const selStart = pathInput.value.indexOf('\\') + 1;
                            const selEnd = pathInput.value.lastIndexOf('\\');
                            pathInput.setSelectionRange(selStart, selEnd);
                        }
                    } else {
                        const pathParts = pathInput.value.split('/');
                        if (pathParts.length > 2) {
                            const selStart = 1;
                            const selEnd = pathInput.value.lastIndexOf('/');
                            pathInput.setSelectionRange(selStart, selEnd);
                        }
                    }
                } catch (e) {
                    // 用户取消选择或发生错误
                    if (e.name !== 'AbortError') {
                        console.error('选择文件夹时出错:', e);
                        showToast('选择文件夹失败: ' + e.message, 'error');
                    }
                }
            } else {
                // 不支持现代API，提示用户手动输入
                showToast('您的浏览器不支持文件夹选择，请手动输入完整路径', 'warning');

                // 根据操作系统类型选择合适的示例路径
                const isWindows = navigator.platform.indexOf('Win') > -1;
                if (isWindows) {
                    pathInput.value = 'C:\\Users\\<USER>\\Videos\\无人播放';
                } else {
                    pathInput.value = '/home/<USER>/videos/无人播放';
                }
            }
        });

        // 提交按钮点击事件
        submitBtn.addEventListener('click', function () {
            const folderPath = pathInput.value.trim();

            if (!folderPath) {
                showToast('请输入文件夹路径!', 'error');
                return;
            }

            // 发送POST请求
            fetch(`${window.protocol}://${window.host}/nblivepath`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ path: folderPath, sessionid: parseInt(document.getElementById('sessionid').value) })
            })
                .then(response => {
                    if (response.ok) {
                        showToast('提交成功!');
                    } else {
                        showToast('提交失败: ' + response.statusText, 'error');
                    }
                })
                .catch(error => {
                    showToast('提交出错: ' + error.message, 'error');
                    console.error('提交错误:', error);
                });
        });

        // 显示提示弹窗
        function showToast(message, type = 'success', duration = 3000) {
            toastMessage.textContent = message;

            // 根据类型设置不同的背景色
            if (type === 'error') {
                successToast.style.backgroundColor = '#f44336';
            } else if (type === 'info') {
                successToast.style.backgroundColor = '#2196F3';
            } else if (type === 'warning') {
                successToast.style.backgroundColor = '#ff9800';
            } else {
                successToast.style.backgroundColor = '#4caf50';
            }

            // 显示弹窗
            successToast.style.display = 'block';

            // 动画显示
            setTimeout(() => {
                successToast.style.opacity = 1;
            }, 10);

            // 指定时间后消失
            setTimeout(() => {
                successToast.style.opacity = 0;

                // 等待淡出动画完成后隐藏
                setTimeout(() => {
                    successToast.style.display = 'none';
                }, 300);
            }, duration);
        }
    }
});

// start();

$(document).ready(function () {


    // 简单的居中函数
    function centerCanvas() {
        const canvas = document.getElementById('canvas');
        const mediaDiv = document.getElementById('media');

        if (!canvas.style.width || !canvas.style.height) {
            // 从全局变量中获取视频流的原始宽高
            // 如果videoRatioWidth和videoRatioHeight已设置，优先使用它们
            let defaultWidth, defaultHeight;

            // 这里考虑到videoRatioWidth和videoRatioHeight可能为null的情况
            if (typeof window.videoRatioWidth === 'number' && typeof window.videoRatioHeight === 'number' &&
                window.videoRatioWidth > 0 && window.videoRatioHeight > 0) {
                // 使用视频流的原始比例
                console.log("使用视频原始比例:", window.videoRatioWidth, window.videoRatioHeight);

                // 如果高度超过1000，按比例缩小
                if (window.videoRatioHeight > 1000) {
                    const scale = 1000 / window.videoRatioHeight;
                    defaultHeight = 1000;
                    defaultWidth = Math.round(window.videoRatioWidth * scale);
                } else {
                    defaultWidth = window.videoRatioWidth;
                    defaultHeight = window.videoRatioHeight;
                }
            } else {
                // 使用默认值
                console.log("使用默认尺寸");
                defaultWidth = 500;
                defaultHeight = 900;

                // 如果视频尺寸暂时不可用，设置一个延迟检查
                const checkAndUpdateSize = function () {
                    if (typeof window.videoRatioWidth === 'number' && typeof window.videoRatioHeight === 'number' &&
                        window.videoRatioWidth > 0 && window.videoRatioHeight > 0) {

                        console.log("延迟检测到视频尺寸:", window.videoRatioWidth, window.videoRatioHeight);

                        // 如果高度超过1000，按比例缩小
                        let newWidth, newHeight;
                        if (window.videoRatioHeight > 1000) {
                            const scale = 1000 / window.videoRatioHeight;
                            newHeight = 1000;
                            newWidth = Math.round(window.videoRatioWidth * scale);
                        } else {
                            newWidth = window.videoRatioWidth;
                            newHeight = window.videoRatioHeight;
                        }

                        // 更新canvas尺寸
                        canvas.style.width = newWidth + 'px';
                        canvas.style.height = newHeight + 'px';

                        // 更新全局变量和控件
                        customWidth = newWidth;
                        customHeight = newHeight;

                        if (customWidthSlider && customWidthValue) {
                            customWidthSlider.value = newWidth;
                            customWidthValue.value = newWidth;
                        }

                        if (customHeightSlider && customHeightValue) {
                            customHeightSlider.value = newHeight;
                            customHeightValue.value = newHeight;
                        }

                        // 重新居中
                        const mediaDivRect = mediaDiv.getBoundingClientRect();
                        const centerX = Math.round((mediaDivRect.width - newWidth) / 2);
                        const centerY = Math.round((mediaDivRect.height - newHeight) / 2);

                        canvas.style.left = centerX + 'px';
                        canvas.style.top = centerY + 'px';

                        // 更新位置滑块
                        if (xOffsetSlider && xOffsetValue) {
                            xOffsetSlider.value = centerX;
                            xOffsetValue.value = centerX;
                        }

                        if (yOffsetSlider && yOffsetValue) {
                            yOffsetSlider.value = centerY;
                            yOffsetValue.value = centerY;
                        }

                        clearTimeout(sizeCheckTimeout);
                    }
                };

                const sizeCheckTimeout = setTimeout(checkAndUpdateSize, 1000);
            }

            // 确保宽高比合理
            customWidth = defaultWidth;
            customHeight = defaultHeight;

            console.log("设置canvas初始尺寸:", defaultWidth, "x", defaultHeight);

            // 设置初始宽高
            canvas.style.width = defaultWidth + 'px';
            canvas.style.height = defaultHeight + 'px';

            // 更新控件
            const customWidthSlider = document.getElementById('customWidthSlider');
            const customWidthValue = document.getElementById('customWidthValue');
            const customHeightSlider = document.getElementById('customHeightSlider');
            const customHeightValue = document.getElementById('customHeightValue');

            if (customWidthSlider && customWidthValue) {
                customWidthSlider.value = defaultWidth;
                customWidthValue.value = defaultWidth;
            }

            if (customHeightSlider && customHeightValue) {
                customHeightSlider.value = defaultHeight;
                customHeightValue.value = defaultHeight;
            }
        }

        const canvasWidth = parseInt(canvas.style.width);
        const canvasHeight = parseInt(canvas.style.height);
        const mediaDivRect = mediaDiv.getBoundingClientRect();

        const centerX = Math.round((mediaDivRect.width - canvasWidth) / 2);
        const centerY = Math.round((mediaDivRect.height - canvasHeight) / 2);

        canvas.style.left = centerX + 'px';
        canvas.style.top = centerY + 'px';

        // 更新位置滑块
        const xOffsetSlider = document.getElementById('xOffsetSlider');
        const xOffsetValue = document.getElementById('xOffsetValue');
        const yOffsetSlider = document.getElementById('yOffsetSlider');
        const yOffsetValue = document.getElementById('yOffsetValue');

        if (xOffsetSlider && xOffsetValue) {
            xOffsetSlider.value = centerX;
            xOffsetValue.value = centerX;
        }

        if (yOffsetSlider && yOffsetValue) {
            yOffsetSlider.value = centerY;
            yOffsetValue.value = centerY;
        }
    }

    // 页面加载和窗口调整时居中
    centerCanvas();
    window.addEventListener('resize', centerCanvas);
    window.addEventListener('load', centerCanvas);

    // 数字人控制面板拖动功能实现
    const settingsModal = document.getElementById('settings-modal');
    const dragHandle = document.getElementById('drag-handle');
    let isDraggingModal = false;
    let offsetXModal, offsetYModal;

    let requestId;

    // 鼠标事件处理
    dragHandle.addEventListener('mousedown', function (e) {
        isDraggingModal = true;

        // 计算鼠标点击位置与面板左上角的偏移
        const rect = settingsModal.getBoundingClientRect();
        offsetXModal = e.clientX - rect.left;
        offsetYModal = e.clientY - rect.top;

        // 防止拖动时选择其他元素
        e.preventDefault();
    });

    document.addEventListener('mousemove', function (e) {

        if (isDraggingModal) {
            if (!requestId) {
                requestId = requestAnimationFrame(() => {
                    // 计算新位置
                    const left = e.clientX - offsetXModal;
                    const top = e.clientY - offsetYModal;

                    // 设置面板位置
                    settingsModal.style.left = left + 'px';
                    settingsModal.style.top = top + 'px';
                    e.stopPropagation(); // 阻止事件冒泡

                    requestId = null;
                });
            }
        }
    });

    document.addEventListener('mouseup', function () {
        isDraggingModal = false;
    });

    // 触摸事件处理（移动设备）
    dragHandle.addEventListener('touchstart', function (e) {
        if (e.touches.length === 1) {
            isDraggingModal = true;

            const touch = e.touches[0];
            const rect = settingsModal.getBoundingClientRect();
            offsetXModal = touch.clientX - rect.left;
            offsetYModal = touch.clientY - rect.top;

            // 阻止默认行为
            e.preventDefault();
        }
    });

    document.addEventListener('touchmove', function (e) {
        if (isDraggingModal && e.touches.length === 1) {
            const touch = e.touches[0];
            const left = touch.clientX - offsetXModal;
            const top = touch.clientY - offsetYModal;  // 修正为使用offsetYModal

            settingsModal.style.left = left + 'px';
            settingsModal.style.top = top + 'px';

            e.preventDefault();
        }
    });

    document.addEventListener('touchend', function () {
        isDraggingModal = false;
    });

    // 颜色选择器显示十六进制值
    const bgColorPicker = document.getElementById('bgColorPicker');
    const bgColorValue = document.getElementById('bgColorValue');

    bgColorPicker.addEventListener('input', function () {
        const color = bgColorPicker.value;
        bgColorValue.value = color;
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);
        bgColorRGB = { r, g, b };
    });

    // 标签页切换
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');

            // 移除所有active类
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 添加active类到当前标签和内容
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // 设置弹窗控制
    const settingsButton = document.getElementById('settings-button');
    const closeModalButton = document.getElementById('close-modal');
    // const modalOverlay = document.getElementById('modal-overlay');

    settingsButton.addEventListener('click', function () {
        settingsModal.style.display = 'block';
        // modalOverlay.style.display = 'block';
    });

    closeModalButton.addEventListener('click', function () {
        settingsModal.style.display = 'none';
        // modalOverlay.style.display = 'none';
    });

    // 为关闭按钮添加触摸事件支持
    closeModalButton.addEventListener('touchend', function (e) {
        e.preventDefault(); // 防止触摸事件被解释为点击
        settingsModal.style.display = 'none';
    });

    // 样式调整，增大关闭按钮点击区域
    closeModalButton.style.padding = '10px';
    closeModalButton.style.fontSize = '24px';

    // modalOverlay.addEventListener('click', function() {
    //     settingsModal.style.display = 'none';
    //     modalOverlay.style.display = 'none';
    // });

    // 原有表单提交逻辑
    // $('#echo-form').on('submit', function(e) {
    //     e.preventDefault();
    //     var message = $('#message').val();
    //     console.log('Sending: ' + message);
    //     console.log('sessionid: ',document.getElementById('sessionid').value);
    //     fetch('http://192.168.3.100:8018/human', {
    //         body: JSON.stringify({
    //             text: message,
    //             type: 'chat',
    //             interrupt: false,
    //             sessionid: parseInt(document.getElementById('sessionid').value),
    //         }),
    //         headers: {
    //             'Content-Type': 'application/json'
    //         },
    //         method: 'POST'
    //     });
    //     $('#message').val('');
    //     // 发送后关闭弹窗
    //     // settingsModal.style.display = 'none';
    //     // modalOverlay.style.display = 'none';
    // });

    // 录制控制逻辑
    $('#btn_start_record').click(function () {
        console.log('Starting recording...');
        fetch(`${window.protocol}://${window.host}/record`, {
            body: JSON.stringify({
                type: 'start_record',
            }),
            headers: {
                'Content-Type': 'application/json'
            },
            method: 'POST'
        }).then(function (response) {
            if (response.ok) {
                console.log('Recording started.');
                $('#btn_start_record').prop('disabled', true);
                $('#btn_stop_record').prop('disabled', false);
            } else {
                console.error('Failed to start recording.');
            }
        }).catch(function (error) {
            console.error('Error:', error);
        });
    });

    $('#btn_stop_record').click(function () {
        console.log('Stopping recording...');
        fetch(`${window.protocol}://${window.host}/record`, {
            body: JSON.stringify({
                type: 'end_record',
            }),
            headers: {
                'Content-Type': 'application/json'
            },
            method: 'POST'
        }).then(function (response) {
            if (response.ok) {
                console.log('Recording stopped.');
                $('#btn_start_record').prop('disabled', false);
                $('#btn_stop_record').prop('disabled', true);
            } else {
                console.error('Failed to stop recording.');
            }
        }).catch(function (error) {
            console.error('Error:', error);
        });
    });

    // Canvas拖动功能实现
    const canvas = document.getElementById('canvas');
    const statusElement = document.getElementById('canvas-status');
    const xOffsetSlider = document.getElementById('xOffsetSlider');
    const xOffsetValue = document.getElementById('xOffsetValue');
    const yOffsetSlider = document.getElementById('yOffsetSlider');
    const yOffsetValue = document.getElementById('yOffsetValue');
    let isDraggable = false;
    let isDragging = false;
    let offsetX, offsetY;

    // 监听空格键
    document.addEventListener('keydown', function (e) {
        if (e.code === 'Space') {
            if (isAltPressed) {
                // 如果Alt键已经激活，则不激活空格键功能
                return;
            }
            isDraggable = true;
            canvas.classList.add('draggable');
            statusElement.textContent = '当前模式：数字人调整 - 鼠标可拖动数字人位置和滚轮缩放';
            statusElement.style.color = 'var(--primary-color)';
        }
        if (e.key === 'Alt') {
            if (isDraggable) {
                // 如果空格键已经激活，则不激活Alt键功能
                return;
            }
            isAltPressed = true;
            statusElement.textContent = '当前模式：背景调整 - 鼠标  可拖动背景位置和滚轮缩放';
            statusElement.style.color = 'var(--secondary-color)';
            // 阻止浏览器默认行为（如菜单显示）
            e.preventDefault();
        }
    });

    document.addEventListener('keyup', function (e) {
        if (e.code === 'Space') {
            isDraggable = false;
            canvas.classList.remove('draggable');
            statusElement.textContent = '按住「空格键」可调整数字人，按「Alt」键可调整背景';
            statusElement.style.color = 'var(--text-secondary)';
        }
        if (e.key === 'Alt') {
            isAltPressed = false;
            isDraggingBg = false;
            statusElement.textContent = '按住「空格键」可调整数字人，按「Alt」键可调整背景';
            statusElement.style.color = 'var(--text-secondary)';
        }
    });

    // 鼠标事件处理
    canvas.addEventListener('mousedown', function (e) {
        if (isDraggable) {
            isDragging = true;

            // 计算鼠标点击位置与Canvas左上角的偏移
            const rect = canvas.getBoundingClientRect();
            offsetX = e.clientX - rect.left;
            offsetY = e.clientY - rect.top;

            // 防止拖动时选择其他元素
            e.preventDefault();
        }
    });

    document.addEventListener('mousemove', function (e) {
        if (isDragging) {
            if (!requestId) {
                requestId = requestAnimationFrame(() => {
                    const mediaDiv = document.getElementById('media');
                    const mediaDivRect = mediaDiv.getBoundingClientRect();
                    const left = e.clientX - mediaDivRect.left - offsetX;
                    const top = e.clientY - mediaDivRect.top - offsetY;

                    canvas.style.left = left + 'px';
                    canvas.style.top = top + 'px';

                    xOffsetSlider.value = left;
                    xOffsetValue.value = left;
                    yOffsetSlider.value = top;
                    yOffsetValue.value = top;

                    requestId = null;
                });
            }
        }
    });

    document.addEventListener('mouseup', function () {
        isDragging = false;
        if (requestId) {
            cancelAnimationFrame(requestId);
            requestId = null;
        }
    });

    // 触摸事件处理（移动设备）
    canvas.addEventListener('touchstart', function (e) {
        if (isDraggable && e.touches.length === 1) {
            isDragging = true;

            const touch = e.touches[0];
            const rect = canvas.getBoundingClientRect();
            offsetX = touch.clientX - rect.left;
            offsetY = touch.clientY - rect.top;

            e.preventDefault();
        }
    });

    document.addEventListener('touchmove', function (e) {
        if (isDragging && e.touches.length === 1) {
            const touch = e.touches[0];
            const mediaDiv = document.getElementById('media');
            const mediaDivRect = mediaDiv.getBoundingClientRect();

            const left = touch.clientX - mediaDivRect.left - offsetX;
            const top = touch.clientY - mediaDivRect.top - offsetY;

            canvas.style.left = left + 'px';
            canvas.style.top = top + 'px';

            // 更新滑块值和显示内容
            xOffsetSlider.value = left;
            xOffsetValue.value = left;
            yOffsetSlider.value = top;
            yOffsetValue.value = top;

            e.preventDefault();
        }
    });

    document.addEventListener('touchend', function () {
        isDragging = false;
    });

    // 处理参数设置控件
    const toleranceSlider = document.getElementById('toleranceSlider');
    const toleranceValue = document.getElementById('toleranceValue');
    const customWidthSlider = document.getElementById('customWidthSlider');
    const customWidthValue = document.getElementById('customWidthValue');
    const customHeightSlider = document.getElementById('customHeightSlider');
    const customHeightValue = document.getElementById('customHeightValue');
    const backgroundUpload = document.getElementById('backgroundUpload');
    const mainContainer = document.querySelector('.main-container');
    mainContainer.style.backgroundImage = 'url(./static/images/bg.png)';

    // 比例锁定逻辑
    const originRatioLockCheckbox = document.getElementById('originRatioLockCheckbox');
    const ratioLockCheckbox = document.getElementById('ratioLockCheckbox');
    let isRatioLocked = false;
    let customRatioLocked = false;

    // 获取初始canvas宽高
    const canvasElement = document.getElementById('canvas');
    let initialWidth = parseInt(canvasElement.style.width) || parseInt(customWidthValue.value) || 500;
    let initialHeight = parseInt(canvasElement.style.height) || parseInt(customHeightValue.value) || 900;
    console.log("当前canvas尺寸:", initialWidth, initialHeight);

    // 设置宽高比 - 优先使用视频流的原始比例
    let aspectRatio;
    // 监听videoRatioWidth和videoRatioHeight的变化
    let checkVideoRatio = setInterval(function () {
        if (typeof window.videoRatioWidth === 'number' && typeof window.videoRatioHeight === 'number' &&
            window.videoRatioWidth > 0 && window.videoRatioHeight > 0) {
            console.log("检测到视频流原始尺寸:", window.videoRatioWidth, window.videoRatioHeight);
            aspectRatio = window.videoRatioWidth / window.videoRatioHeight;

            // 如果原始比例锁定被勾选，则立即应用比例
            if (isRatioLocked) {
                const currentHeight = parseInt(canvas.style.height) || parseInt(customHeightSlider.value);
                const newWidth = Math.round(currentHeight * aspectRatio);
                customWidth = newWidth;
                customWidthSlider.value = newWidth;
                customWidthValue.value = newWidth;
                canvas.style.width = newWidth + 'px';
            }

            clearInterval(checkVideoRatio);
        }
    }, 500); // 每500ms检查一次

    // 默认先使用当前canvas尺寸计算比例
    aspectRatio = initialWidth / initialHeight;
    let customAspectRatio = initialWidth / initialHeight;

    // 原始比例锁定
    originRatioLockCheckbox.addEventListener('change', function () {
        isRatioLocked = this.checked;
        if (isRatioLocked) {
            // 确保自定义比例锁定被关闭
            if (customRatioLocked) {
                ratioLockCheckbox.checked = false;
                customRatioLocked = false;
            }

            // 获取当前canvas的实际尺寸和位置
            const canvas = document.getElementById('canvas');
            const currentWidth = parseInt(canvas.style.width) || parseInt(customWidthSlider.value);
            const currentHeight = parseInt(canvas.style.height) || parseInt(customHeightSlider.value);
            const currentLeft = parseInt(canvas.style.left) || 0;
            const currentTop = parseInt(canvas.style.top) || 0;

            // 计算当前中心点
            const centerX = currentLeft + currentWidth / 2;
            const centerY = currentTop + currentHeight / 2;

            // 根据当前高度和原始比例计算新宽度
            const newWidth = Math.round(currentHeight * aspectRatio);

            // 更新宽度值
            customWidth = newWidth;
            customWidthSlider.value = newWidth;
            customWidthValue.value = newWidth;

            // 更新canvas的绘图尺寸
            canvas.width = newWidth;
            canvas.height = currentHeight;

            // 更新样式尺寸
            canvas.style.width = newWidth + 'px';

            // 计算新位置以保持中心点不变
            const newLeft = Math.round(centerX - newWidth / 2);

            // 更新位置
            canvas.style.left = newLeft + 'px';

            // 更新位置滑块
            xOffsetSlider.value = newLeft;
            xOffsetValue.value = newLeft;

            console.log('已根据当前高度调整宽度以匹配原始比例:', currentHeight, '高 ×', newWidth, '宽');
        }
    });

    // 自定义比例锁定
    ratioLockCheckbox.addEventListener('change', function () {
        customRatioLocked = this.checked;
        if (customRatioLocked) {
            // 确保原始比例锁定被关闭
            if (isRatioLocked) {
                originRatioLockCheckbox.checked = false;
                isRatioLocked = false;
            }

            // 获取当前canvas尺寸和位置
            const canvas = document.getElementById('canvas');
            const currentWidth = parseInt(canvas.style.width) || parseInt(customWidthSlider.value);
            const currentHeight = parseInt(canvas.style.height) || parseInt(customHeightSlider.value);
            const currentLeft = parseInt(canvas.style.left) || 0;
            const currentTop = parseInt(canvas.style.top) || 0;

            // 计算中心点位置
            const centerX = currentLeft + currentWidth / 2;
            const centerY = currentTop + currentHeight / 2;

            // 计算当前比例
            customAspectRatio = currentWidth / currentHeight;

            console.log('已锁定自定义比例:', customAspectRatio.toFixed(2), '(宽/高)');
        }
    });

    backgroundUpload.addEventListener('change', function () {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
                mainContainer.style.backgroundImage = `url(${e.target.result})`;
                // 上传新背景后应用当前的位置和缩放设置
                updateBackgroundStyle();
            };
            reader.readAsDataURL(file);
        }
    });

    // 初始化绿幕容差
    toleranceSlider.addEventListener('input', function () {
        tolerance = parseInt(toleranceSlider.value);
        toleranceValue.value = tolerance;
    });

    // 初始化数字人X坐标
    xOffsetSlider.addEventListener('input', function () {
        const value = parseInt(xOffsetSlider.value);
        xOffsetValue.value = value;
        canvas.style.left = value + 'px';
    });

    // 初始化数字人Y坐标
    yOffsetSlider.addEventListener('input', function () {
        const value = parseInt(yOffsetSlider.value);
        yOffsetValue.value = value;
        canvas.style.top = value + 'px';
    });

    // 页面加载后初始化数字人到底部居中位置
    window.addEventListener('load', function () {
        // 确保canvas和media元素已加载
        const canvas = document.getElementById('canvas');
        const mediaDiv = document.getElementById('media');

        if (canvas && mediaDiv) {
            setTimeout(function () {
                positionDigitalHuman('centerBottom', 0); // 无动画直接定位

                // 更新UI滑块值
                const left = parseInt(canvas.style.left) || 0;
                const top = parseInt(canvas.style.top) || 0;
                xOffsetSlider.value = left;
                xOffsetValue.value = left;
                yOffsetSlider.value = top;
                yOffsetValue.value = top;
            }, 1500); // 延迟500ms确保页面元素都已加载完成
        }
    });

    // 初始化视频宽度，考虑锁定比例
    customWidthSlider.addEventListener('input', function () {
        const canvas = document.getElementById('canvas');
        const currentWidth = parseInt(canvas.style.width);
        const currentHeight = parseInt(canvas.style.height);
        const currentLeft = parseInt(canvas.style.left) || 0;
        const currentTop = parseInt(canvas.style.top) || 0;

        // 计算当前中心点
        const centerX = currentLeft + currentWidth / 2;
        const centerY = currentTop + currentHeight / 2;

        // 获取新宽度
        const newWidth = parseInt(customWidthSlider.value);
        customWidth = newWidth;
        customWidthValue.value = newWidth;

        // 计算新高度（根据比例锁定状态）
        let newHeight;
        if (isRatioLocked) {
            // 使用原始比例
            newHeight = Math.round(newWidth / aspectRatio);
        } else if (customRatioLocked) {
            // 使用自定义比例
            newHeight = Math.round(newWidth / customAspectRatio);
        } else {
            // 保持当前高度
            newHeight = currentHeight;
        }

        // 更新高度相关值
        customHeight = newHeight;
        customHeightSlider.value = newHeight;
        customHeightValue.value = newHeight;

        // 更新canvas的绘图尺寸
        canvas.width = newWidth;
        canvas.height = newHeight;

        // 更新样式尺寸
        canvas.style.width = newWidth + 'px';
        canvas.style.height = newHeight + 'px';

        // 计算新位置以保持中心点不变
        const newLeft = Math.round(centerX - newWidth / 2);
        const newTop = Math.round(centerY - newHeight / 2);

        // 更新位置
        canvas.style.left = newLeft + 'px';
        canvas.style.top = newTop + 'px';

        // 更新位置滑块
        xOffsetSlider.value = newLeft;
        xOffsetValue.value = newLeft;
        yOffsetSlider.value = newTop;
        yOffsetValue.value = newTop;
    });

    // 初始化视频高度，考虑锁定比例，并保持中心点不变
    customHeightSlider.addEventListener('input', function () {
        const canvas = document.getElementById('canvas');
        const currentWidth = parseInt(canvas.style.width);
        const currentHeight = parseInt(canvas.style.height);
        const currentLeft = parseInt(canvas.style.left) || 0;
        const currentTop = parseInt(canvas.style.top) || 0;

        // 计算当前中心点
        const centerX = currentLeft + currentWidth / 2;
        const centerY = currentTop + currentHeight / 2;

        // 获取新高度
        const newHeight = parseInt(customHeightSlider.value);
        customHeight = newHeight;
        customHeightValue.value = newHeight;

        // 计算新宽度（根据比例锁定状态）
        let newWidth;
        if (isRatioLocked) {
            // 使用原始比例
            newWidth = Math.round(newHeight * aspectRatio);
        } else if (customRatioLocked) {
            // 使用自定义比例
            newWidth = Math.round(newHeight * customAspectRatio);
        } else {
            // 保持当前宽度
            newWidth = currentWidth;
        }

        // 更新宽度相关值
        customWidth = newWidth;
        customWidthSlider.value = newWidth;
        customWidthValue.value = newWidth;

        // 更新canvas的绘图尺寸
        canvas.width = newWidth;
        canvas.height = newHeight;

        // 更新样式尺寸
        canvas.style.width = newWidth + 'px';
        canvas.style.height = newHeight + 'px';

        // 计算新位置以保持中心点不变
        const newLeft = Math.round(centerX - newWidth / 2);
        const newTop = Math.round(centerY - newHeight / 2);

        // 更新位置
        canvas.style.left = newLeft + 'px';
        canvas.style.top = newTop + 'px';

        // 更新位置滑块
        xOffsetSlider.value = newLeft;
        xOffsetValue.value = newLeft;
        yOffsetSlider.value = newTop;
        yOffsetValue.value = newTop;
    });

    document.addEventListener('wheel', function (e) {
        if (isDraggable) {
            const scaleFactor = 0.1; // 缩放比例

            // 获取当前画布位置和尺寸
            let currentWidth = parseInt(canvas.style.width) || initialWidth;
            let currentHeight = parseInt(canvas.style.height) || initialHeight;
            let currentLeft = parseInt(canvas.style.left) || 0;
            let currentTop = parseInt(canvas.style.top) || 0;

            // 检查位置是否设置，未设置则先居中
            if (!canvas.style.left || !canvas.style.top) {
                centerCanvas();
                currentLeft = parseInt(canvas.style.left);
                currentTop = parseInt(canvas.style.top);
            }

            // 计算画布中心点坐标（相对于父容器）
            const centerX = currentLeft + currentWidth / 2;
            const centerY = currentTop + currentHeight / 2;

            // 计算新尺寸
            let newWidth, newHeight;
            if (e.deltaY < 0) { // 滚轮向上滚动 - 放大
                newWidth = Math.round(currentWidth * (1 + scaleFactor));
                if (isRatioLocked) {
                    // 使用原始比例
                    newHeight = Math.round(newWidth / aspectRatio);
                } else if (customRatioLocked) {
                    // 使用自定义比例
                    newHeight = Math.round(newWidth / customAspectRatio);
                } else {
                    // 未锁定比例但仍保持当前比例进行缩放
                    const currentRatio = currentHeight > 0 ? currentWidth / currentHeight : aspectRatio;
                    newHeight = Math.round(newWidth / (isFinite(currentRatio) ? currentRatio : aspectRatio));
                }
            } else { // 滚轮向下滚动 - 缩小
                newWidth = Math.round(currentWidth * (1 - scaleFactor));
                if (isRatioLocked) {
                    // 使用原始比例
                    newHeight = Math.round(newWidth / aspectRatio);
                } else if (customRatioLocked) {
                    // 使用自定义比例
                    newHeight = Math.round(newWidth / customAspectRatio);
                } else {
                    // 未锁定比例但仍保持当前比例进行缩放
                    const currentRatio = currentHeight > 0 ? currentWidth / currentHeight : aspectRatio;
                    newHeight = Math.round(newWidth / (isFinite(currentRatio) ? currentRatio : aspectRatio));
                }
            }

            // 计算新的左上角坐标，以保持中心点不变
            const newLeft = Math.round(centerX - newWidth / 2);
            const newTop = Math.round(centerY - newHeight / 2);

            // 更新画布位置和尺寸
            canvas.style.width = newWidth + 'px';
            canvas.style.height = newHeight + 'px';
            canvas.style.left = newLeft + 'px';
            canvas.style.top = newTop + 'px';

            // 新增：更新canvas实际绘图尺寸，解决滚轮缩放导致模糊的问题
            canvas.width = newWidth;
            canvas.height = newHeight;

            // 新增：更新全局变量，确保drawFrame函数使用新尺寸
            customWidth = newWidth;
            customHeight = newHeight;

            // 更新滑块值和显示内容
            const customWidthSlider = document.getElementById('customWidthSlider');
            const customWidthValue = document.getElementById('customWidthValue');
            const customHeightSlider = document.getElementById('customHeightSlider');
            const customHeightValue = document.getElementById('customHeightValue');

            customWidthSlider.value = newWidth;
            customWidthValue.value = newWidth;
            customHeightSlider.value = newHeight;
            customHeightValue.value = newHeight;

            // 更新位置滑块
            xOffsetSlider.value = newLeft;
            xOffsetValue.value = newLeft;
            yOffsetSlider.value = newTop;
            yOffsetValue.value = newTop;
        }
    });

    // 为 bgColorValue 添加监听器
    bgColorValue.addEventListener('input', function () {
        const color = this.value;
        bgColorPicker.value = color;
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);
        bgColorRGB = { r, g, b };
    });

    // 为 xOffsetValue 添加监听器
    xOffsetValue.addEventListener('input', function () {
        const value = parseInt(this.value);
        xOffsetSlider.value = value;
        canvas.style.left = value + 'px';
    });

    // 为 yOffsetValue 添加监听器
    yOffsetValue.addEventListener('input', function () {
        const value = parseInt(this.value);
        yOffsetSlider.value = value;
        canvas.style.top = value + 'px';
    });

    // 为 toleranceValue 添加监听器
    toleranceValue.addEventListener('input', function () {
        const value = parseInt(this.value);
        toleranceSlider.value = value;
        tolerance = value;
    });

    // 为 customWidthValue 添加监听器
    customWidthValue.addEventListener('input', function () {
        const canvas = document.getElementById('canvas');
        const currentWidth = parseInt(canvas.style.width);
        const currentHeight = parseInt(canvas.style.height);
        const currentLeft = parseInt(canvas.style.left) || 0;
        const currentTop = parseInt(canvas.style.top) || 0;

        // 计算当前中心点
        const centerX = currentLeft + currentWidth / 2;
        const centerY = currentTop + currentHeight / 2;

        // 获取新宽度
        const newWidth = parseInt(this.value);
        customWidth = newWidth;
        customWidthSlider.value = newWidth;

        // 计算新高度（根据比例锁定状态）
        let newHeight = currentHeight;
        if (isRatioLocked) {
            // 使用原始比例
            newHeight = Math.round(newWidth / aspectRatio);
            customHeight = newHeight;
            customHeightSlider.value = newHeight;
            customHeightValue.value = newHeight;
        } else if (customRatioLocked) {
            // 使用自定义比例
            newHeight = Math.round(newWidth / customAspectRatio);
            customHeight = newHeight;
            customHeightSlider.value = newHeight;
            customHeightValue.value = newHeight;
        }

        // 更新canvas的绘图尺寸
        canvas.width = newWidth;
        canvas.height = newHeight;

        // 更新样式尺寸
        canvas.style.width = newWidth + 'px';
        canvas.style.height = newHeight + 'px';

        // 计算新位置以保持中心点不变
        const newLeft = Math.round(centerX - newWidth / 2);
        const newTop = Math.round(centerY - newHeight / 2);

        // 更新位置
        canvas.style.left = newLeft + 'px';
        canvas.style.top = newTop + 'px';

        // 更新位置滑块
        xOffsetSlider.value = newLeft;
        xOffsetValue.value = newLeft;
        yOffsetSlider.value = newTop;
        yOffsetValue.value = newTop;
    });

    // 为 customHeightValue 添加监听器
    customHeightValue.addEventListener('input', function () {
        const canvas = document.getElementById('canvas');
        const currentWidth = parseInt(canvas.style.width);
        const currentHeight = parseInt(canvas.style.height);
        const currentLeft = parseInt(canvas.style.left) || 0;
        const currentTop = parseInt(canvas.style.top) || 0;

        // 计算当前中心点
        const centerX = currentLeft + currentWidth / 2;
        const centerY = currentTop + currentHeight / 2;

        // 获取新高度
        const newHeight = parseInt(this.value);
        customHeight = newHeight;
        customHeightSlider.value = newHeight;

        // 计算新宽度（根据比例锁定状态）
        let newWidth = currentWidth;
        if (isRatioLocked) {
            // 使用原始比例
            newWidth = Math.round(newHeight * aspectRatio);
            customWidth = newWidth;
            customWidthSlider.value = newWidth;
            customWidthValue.value = newWidth;
        } else if (customRatioLocked) {
            // 使用自定义比例
            newWidth = Math.round(newHeight * customAspectRatio);
            customWidth = newWidth;
            customWidthSlider.value = newWidth;
            customWidthValue.value = newWidth;
        }

        // 更新canvas的绘图尺寸
        canvas.width = newWidth;
        canvas.height = newHeight;

        // 更新样式尺寸
        canvas.style.width = newWidth + 'px';
        canvas.style.height = newHeight + 'px';

        // 计算新位置以保持中心点不变
        const newLeft = Math.round(centerX - newWidth / 2);
        const newTop = Math.round(centerY - newHeight / 2);

        // 更新位置
        canvas.style.left = newLeft + 'px';
        canvas.style.top = newTop + 'px';

        // 更新位置滑块
        xOffsetSlider.value = newLeft;
        xOffsetValue.value = newLeft;
        yOffsetSlider.value = newTop;
        yOffsetValue.value = newTop;
    });

    // 新增：抗锯齿拖动条
    const antialiasingSlider = document.getElementById('antialiasingSlider');
    const antialiasingValue = document.getElementById('antialiasingValue');

    // 初始化抗锯齿强度
    // 在webrtcapi-asr.html中修改事件监听器
    antialiasingSlider.addEventListener('input', function () {
        const value = parseInt(antialiasingSlider.value);
        antialiasingValue.value = value;
        adjustAntiAliasingStrength(value);
    });

    // 为 antialiasingValue 添加监听器
    antialiasingValue.addEventListener('input', function () {
        const value = parseInt(this.value);
        antialiasingSlider.value = value;
        adjustAntiAliasingStrength(value);
    });

    // 背景位置和缩放调整
    const bgXOffsetSlider = document.getElementById('bgXOffsetSlider');
    const bgXOffsetValue = document.getElementById('bgXOffsetValue');
    const bgYOffsetSlider = document.getElementById('bgYOffsetSlider');
    const bgYOffsetValue = document.getElementById('bgYOffsetValue');
    const bgScaleSlider = document.getElementById('bgScaleSlider');
    const bgScaleValue = document.getElementById('bgScaleValue');

    // 更新背景样式的函数
    function updateBackgroundStyle() {
        const xPos = parseInt(bgXOffsetValue.value);
        const yPos = parseInt(bgYOffsetValue.value);
        const scale = parseInt(bgScaleValue.value);

        mainContainer.style.backgroundPosition = `calc(50% + ${xPos}px) calc(50% + ${yPos}px)`;
        mainContainer.style.backgroundSize = `${scale}%`;
    }

    // 初始化背景X坐标
    bgXOffsetSlider.addEventListener('input', function () {
        const value = parseInt(bgXOffsetSlider.value);
        bgXOffsetValue.value = value;
        updateBackgroundStyle();
    });

    // 初始化背景Y坐标
    bgYOffsetSlider.addEventListener('input', function () {
        const value = parseInt(bgYOffsetSlider.value);
        bgYOffsetValue.value = value;
        updateBackgroundStyle();
    });

    // 初始化背景缩放
    bgScaleSlider.addEventListener('input', function () {
        const value = parseInt(bgScaleSlider.value);
        bgScaleValue.value = value;
        updateBackgroundStyle();
    });

    // 为背景X坐标输入框添加监听器
    bgXOffsetValue.addEventListener('input', function () {
        const value = parseInt(this.value);
        bgXOffsetSlider.value = value;
        updateBackgroundStyle();
    });

    // 为背景Y坐标输入框添加监听器
    bgYOffsetValue.addEventListener('input', function () {
        const value = parseInt(this.value);
        bgYOffsetSlider.value = value;
        updateBackgroundStyle();
    });

    // 为背景缩放输入框添加监听器
    bgScaleValue.addEventListener('input', function () {
        const value = parseInt(this.value);
        bgScaleSlider.value = value;
        updateBackgroundStyle();
    });

    // 背景拖动和缩放功能
    let isAltPressed = false;
    let isDraggingBg = false;
    let bgOffsetX, bgOffsetY;
    let lastMouseX, lastMouseY;

    // 鼠标事件处理
    document.addEventListener('mousedown', function (e) {
        if (isAltPressed) {
            isDraggingBg = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
            e.preventDefault();
        }
    });

    document.addEventListener('mousemove', function (e) {
        if (isDraggingBg && isAltPressed) {
            // 计算鼠标移动的距离
            const deltaX = e.clientX - lastMouseX;
            const deltaY = e.clientY - lastMouseY;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;

            // 更新背景位置
            const currentX = parseInt(bgXOffsetValue.value);
            const currentY = parseInt(bgYOffsetValue.value);
            const newX = Math.max(-1900, Math.min(1900, currentX + deltaX));
            const newY = Math.max(-1900, Math.min(1900, currentY + deltaY));

            // 更新滑块和输入框的值
            bgXOffsetSlider.value = newX;
            bgXOffsetValue.value = newX;
            bgYOffsetSlider.value = newY;
            bgYOffsetValue.value = newY;

            // 应用更新
            updateBackgroundStyle();
        }
    });

    document.addEventListener('mouseup', function () {
        isDraggingBg = false;
    });

    // 滚轮事件处理背景缩放
    document.addEventListener('wheel', function (e) {
        if (isAltPressed) {
            e.preventDefault();

            // 获取当前缩放值
            let currentScale = parseInt(bgScaleValue.value);

            // 根据滚轮方向调整缩放，使调整更灵敏
            const adjustStep = currentScale <= 100 ? 5 : 10; // 在小尺寸时更精细调整

            if (e.deltaY < 0) { // 向上滚动 - 放大
                currentScale = Math.min(200, currentScale + adjustStep);
            } else { // 向下滚动 - 缩小
                currentScale = Math.max(50, currentScale - adjustStep);
            }

            // 更新滑块和输入框的值
            bgScaleSlider.value = currentScale;
            bgScaleValue.value = currentScale;

            // 应用更新
            updateBackgroundStyle();
        }
    }, { passive: false });

    // 更新状态提示
    statusElement.textContent = '按住「空格键」可调整数字人，按「Alt」键可调整背景';

    // 数字人位置快速定位
    const positionLeftBottom = document.getElementById('positionLeftBottom');
    const positionRightBottom = document.getElementById('positionRightBottom');
    const positionCenterBottom = document.getElementById('positionCenterBottom');

    positionLeftBottom.addEventListener('click', function () {
        positionDigitalHuman('leftBottom', 1.2);
    });

    positionRightBottom.addEventListener('click', function () {
        const canvas = document.getElementById('canvas');
        const mediaDiv = document.getElementById('media');
        const mediaDivRect = mediaDiv.getBoundingClientRect();
        const canvasWidth = parseInt(canvas.style.width || customWidthValue.value);
        const canvasHeight = parseInt(canvas.style.height || customHeightValue.value);

        // 计算右下角位置
        const left = mediaDivRect.width - canvasWidth;
        const top = mediaDivRect.height - canvasHeight;

        // 设置位置
        canvas.style.left = left + 'px';
        canvas.style.top = top + 'px';

        // 更新滑块
        xOffsetSlider.value = left;
        xOffsetValue.value = left;
        yOffsetSlider.value = top;
        yOffsetValue.value = top;
    });

    // 居中到屏幕中心
    const positionCenterMiddle = document.getElementById('positionCenterMiddle');
    positionCenterMiddle.addEventListener('click', function (e) {
        e.stopPropagation(); // 防止事件冒泡
        positionDigitalHuman('centerMiddle', 0.4);
    });

    // 居中到底部
    positionCenterBottom.addEventListener('click', function (e) {
        e.stopPropagation(); // 防止事件冒泡
        positionDigitalHuman('centerBottom', 0.4);
    });

    // 右下角定位
    positionRightBottom.addEventListener('click', function () {
        positionDigitalHuman('rightBottom', 0.4);
    });

    // 背景全屏适配
    const bgFitWidth = document.getElementById('bgFitWidth');
    const bgFitHeight = document.getElementById('bgFitHeight');

    bgFitWidth.addEventListener('click', function () {
        // 设置背景宽度铺满
        bgScaleSlider.value = 100;
        bgScaleValue.value = 100;
        bgXOffsetSlider.value = 0;
        bgXOffsetValue.value = 0;
        mainContainer.style.backgroundSize = '100% auto';
        mainContainer.style.backgroundPosition = 'center center';

        // 更新Y轴位置保持不变
        const yPos = parseInt(bgYOffsetValue.value);
        mainContainer.style.backgroundPosition = `center calc(50% + ${yPos}px)`;
    });

    bgFitHeight.addEventListener('click', function () {
        // 设置背景高度铺满
        bgScaleSlider.value = 100;
        bgScaleValue.value = 100;
        bgYOffsetSlider.value = 0;
        bgYOffsetValue.value = 0;
        mainContainer.style.backgroundSize = 'auto 100%';
        mainContainer.style.backgroundPosition = 'center center';

        // 更新X轴位置保持不变
        const xPos = parseInt(bgXOffsetValue.value);
        mainContainer.style.backgroundPosition = `calc(50% + ${xPos}px) center`;
    });

    // 音频播放与语音识别自动切换功能
    let asrIframe = document.getElementById('asr-iframe');
    let audioElement = document.getElementById('audio');
    let isAudioMonitorRunning = false;
    let volumeThreshold = 0.05; // 音量阈值，可以根据需要调整
    let audioContext;
    let mediaStreamSource;
    let analyser;
    let asrEnabled = true;
    let isWebRTCStreamAvailable = false;

    // 确保页面已加载ASR iframe
    function ensureAsrIframeReady() {
        if (!asrIframe || !asrIframe.contentWindow) {
            asrIframe = document.getElementById('asr-iframe');
            if (!asrIframe) {
                console.error('找不到ASR iframe元素');
                return false;
            }
        }

        // 检查iframe是否已加载
        try {
            if (typeof asrIframe.contentWindow.pauseASRRecording !== 'function') {
                console.log('ASR iframe还未完全加载，稍后重试...');
                return false;
            }
        } catch (e) {
            console.error('无法访问ASR iframe内容:', e);
            return false;
        }

        return true;
    }

    // 尝试从WebRTC获取音频流
    function setupWebRTCAudioMonitoring() {
        if (window.pc) {
            try {
                // 等待PC准备就绪并有接收器
                let checkReceivers = setInterval(() => {
                    if (window.pc.getReceivers && window.pc.getReceivers().length > 0) {
                        clearInterval(checkReceivers);

                        // 寻找音频轨道
                        const audioReceiver = window.pc.getReceivers().find(r => r.track && r.track.kind === 'audio');
                        if (audioReceiver && audioReceiver.track) {
                            console.log("找到WebRTC音频轨道，设置音频监控");
                            const audioStream = new MediaStream([audioReceiver.track]);

                            // 创建音频分析器
                            try {
                                setupAudioAnalyser(audioStream);
                                isWebRTCStreamAvailable = true;
                            } catch (e) {
                                console.error("WebRTC音频分析器设置失败:", e);
                                fallbackToElementSrcObject();
                            }
                        } else {
                            console.log("未找到WebRTC音频轨道，使用后备方案");
                            fallbackToElementSrcObject();
                        }
                    }
                }, 1000);

                // 5秒后如果还未成功，使用备用方案
                setTimeout(() => {
                    if (!isWebRTCStreamAvailable) {
                        clearInterval(checkReceivers);
                        console.log("WebRTC流获取超时，使用后备方案");
                        fallbackToElementSrcObject();
                    }
                }, 5000);

            } catch (e) {
                console.error("WebRTC流获取失败:", e);
                fallbackToElementSrcObject();
            }
        } else {
            console.log("WebRTC PC不可用，使用后备方案");
            fallbackToElementSrcObject();
        }
    }

    // 备用方案：使用音频元素的srcObject
    function fallbackToElementSrcObject() {
        if (audioElement && audioElement.srcObject) {
            console.log("使用audio元素的srcObject");
            try {
                setupAudioAnalyser(audioElement.srcObject);
            } catch (e) {
                console.error("音频分析器设置失败，切换到播放状态监听:", e);
                setupPlaybackEvents();
            }
        } else if (audioElement) {
            console.log("音频元素没有srcObject，使用播放状态事件监听");
            setupPlaybackEvents();
        } else {
            console.log("找不到音频元素，无法进行音量监控");
        }
    }

    // 设置音频分析器
    function setupAudioAnalyser(stream) {
        try {
            // 确保先关闭之前的audioContext
            if (audioContext) {
                try {
                    audioContext.close();
                } catch (e) {
                    console.error("关闭之前的audioContext失败:", e);
                }
            }

            // 创建新的audioContext
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            analyser.fftSize = 256;

            mediaStreamSource = audioContext.createMediaStreamSource(stream);
            mediaStreamSource.connect(analyser);

            isAudioMonitorRunning = true;
            monitorAudioVolume();
            console.log('音频分析器已设置');
        } catch (e) {
            console.error("设置音频分析器失败:", e);
            throw e;
        }
    }

    // 初始化音频监控
    function initAudioMonitoring() {
        if (isAudioMonitorRunning) {
            console.log("音频监控已经在运行中");
            return;
        }

        console.log("正在初始化音频监控...");
        setupWebRTCAudioMonitoring();
    }

    // 监控音频音量
    function monitorAudioVolume() {
        if (!isAudioMonitorRunning || !analyser) {
            console.log("音频监控已停止");
            return;
        }

        try {
            // 创建数据数组接收分析数据
            const dataArray = new Uint8Array(analyser.frequencyBinCount);

            // 获取音量数据
            analyser.getByteFrequencyData(dataArray);

            // 计算平均音量
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
                sum += dataArray[i];
            }
            const average = sum / dataArray.length / 255; // 归一化到0-1范围

            // 使用平滑化防止频繁切换
            const smoothFactor = 0.3; // 平滑因子
            if (!window.smoothedVolume) window.smoothedVolume = average;
            window.smoothedVolume = window.smoothedVolume * (1 - smoothFactor) + average * smoothFactor;

            // 将数据发送到ASR iframe (转换为0-100范围)
            sendVolumeDataToASR(window.smoothedVolume * 100);

            // 根据平滑后的音量控制语音识别 
            if (window.smoothedVolume > volumeThreshold) {
                if (asrEnabled) {
                    pauseASR();
                    console.log('音量超过阈值，暂停语音识别:', window.smoothedVolume.toFixed(3));
                }
            } else {
                if (!asrEnabled && window.smoothedVolume < volumeThreshold * 0.8) { // 添加一些滞后，避免在阈值边缘频繁切换
                    resumeASR();
                    console.log('音量低于阈值，恢复语音识别:', window.smoothedVolume.toFixed(3));
                }
            }

            // 继续监控
            requestAnimationFrame(monitorAudioVolume);
        } catch (e) {
            console.error("音量监控出错:", e);
            isAudioMonitorRunning = false;

            // 出错后切换到播放状态监听
            setupPlaybackEvents();
        }
    }

    // 备用方案：基于播放状态的事件监听
    function setupPlaybackEvents() {
        console.log("使用播放状态监听方案");

        // 移除可能已存在的事件监听器
        audioElement.removeEventListener('play', onAudioPlay);
        audioElement.removeEventListener('pause', onAudioPause);
        audioElement.removeEventListener('ended', onAudioEnd);

        // 添加新的事件监听器
        audioElement.addEventListener('play', onAudioPlay);
        audioElement.addEventListener('pause', onAudioPause);
        audioElement.addEventListener('ended', onAudioEnd);

        console.log('音频播放事件监听器已初始化');
    }

    function onAudioPlay() {
        if (asrEnabled) {
            pauseASR();
            console.log('音频开始播放，暂停语音识别');
        }
    }

    function onAudioPause() {
        if (!asrEnabled) {
            resumeASR();
            console.log('音频暂停播放，恢复语音识别');
        }
    }

    function onAudioEnd() {
        if (!asrEnabled) {
            resumeASR();
            console.log('音频播放结束，恢复语音识别');
        }
    }

    // 暂停语音识别
    function pauseASR() {
        try {
            const asrIframe = document.getElementById('asr-iframe');
            if (asrIframe && asrIframe.contentWindow) {
                // 使用postMessage发送暂停命令，而不是直接调用iframe方法
                asrIframe.contentWindow.postMessage({
                    type: 'asr_control',
                    action: 'pause'
                }, '*');
                asrEnabled = false;
                console.log('已发送暂停语音识别命令');
            } else {
                console.error('找不到ASR iframe，无法暂停语音识别');
            }
        } catch (e) {
            console.error('暂停语音识别失败:', e);
        }
    }

    // 恢复语音识别
    function resumeASR() {
        try {
            const asrIframe = document.getElementById('asr-iframe');
            if (asrIframe && asrIframe.contentWindow) {
                // 使用postMessage发送恢复命令，而不是直接调用iframe方法
                asrIframe.contentWindow.postMessage({
                    type: 'asr_control',
                    action: 'resume'
                }, '*');
                asrEnabled = true;
                console.log('已发送恢复语音识别命令');
            } else {
                console.error('找不到ASR iframe，无法恢复语音识别');
            }
        } catch (e) {
            console.error('恢复语音识别失败:', e);
        }
    }

    // 监听音频流创建，此处监听start按钮点击
    // document.getElementById('start').addEventListener('click', function () {
    //     console.log("监听到start按钮点击，准备初始化音频监控");
    //     // 延迟初始化，确保WebRTC连接建立
    //     setTimeout(function () {
    //         initAudioMonitoring();
    //     }, 3000);
    // });

    // 通过postMessage向ASR iframe发送音量数据
    function sendVolumeDataToASR(volume) {
        try {
            const asrIframe = document.getElementById('asr-iframe');
            if (asrIframe && asrIframe.contentWindow) {
                asrIframe.contentWindow.postMessage({
                    type: 'audio_volume',
                    volume: {
                        average: volume,
                        time: Date.now()
                    }
                }, '*');
            }
        } catch (e) {
            console.error("向ASR iframe发送音量数据失败:", e);
        }
    }

    // 在页面加载时尝试初始化
    document.addEventListener('DOMContentLoaded', function () {
        console.log("页面已加载，准备初始化");

        // 如果已经有活动连接，尝试初始化音频监控
        setTimeout(function () {
            if (document.getElementById('stop').style.display === 'inline-block') {
                console.log("检测到活动连接，初始化音频监控");
                initAudioMonitoring();
            } else {
                console.log("未检测到活动连接，等待start按钮点击");
            }
        }, 2000);
    });

    // 监听来自ASR iframe的消息
    window.addEventListener('message', function (event) {
        // 可以根据需要检查来源
        // if (event.origin !== window.location.origin) return;

        if (event.data && event.data.type === 'asr_ready') {
            console.log("收到ASR iframe准备就绪消息");
            // 启动音频监控并发送数据
            if (!isAudioMonitorRunning) {
                initAudioMonitoring();
            }
        }
    });

    // 对话记录弹窗拖动功能实现
    // 优化后的示例
    const chatModal = document.getElementById('chat-modal');
    let isChatModalDragging = false;
    let chatModalOffsetX, chatModalOffsetY;


    // 鼠标拖动事件
    chatModal.addEventListener('mousedown', (e) => {
        // 如果点击的是关闭按钮或控制元素，不触发拖动
        if (e.target.closest('.close-button') ||
            e.target.tagName === 'INPUT' ||
            e.target.tagName === 'BUTTON' ||
            e.target.closest('.form-group')) return;

        // 阻止默认行为，防止文本选择
        e.preventDefault();

        isChatModalDragging = true;

        // 获取当前位置
        const rect = chatModal.getBoundingClientRect();

        // 计算鼠标点击位置相对于模态框的偏移
        chatModalOffsetX = e.clientX - rect.left;
        chatModalOffsetY = e.clientY - rect.top;

        // 保存原始样式，用于在拖动后恢复
        chatModal.dataset.originalTransform = chatModal.style.transform || '';
        chatModal.dataset.originalLeft = chatModal.style.left || '';
        chatModal.dataset.originalTop = chatModal.style.top || '';
        chatModal.dataset.originalRight = chatModal.style.right || '';
        chatModal.dataset.originalBottom = chatModal.style.bottom || '';

        // 确保在拖动前清除transform，防止影响拖动
        chatModal.style.transform = 'none';

        // 设置为绝对定位，确保可以通过left/top移动
        chatModal.style.position = 'absolute';
        chatModal.style.bottom = 'auto';
        chatModal.style.right = 'auto';
        chatModal.style.left = rect.left + 'px';
        chatModal.style.top = rect.top + 'px';

        // 增加视觉反馈
        chatModal.style.cursor = 'grabbing';
        chatModal.style.userSelect = 'none';
    });

    document.addEventListener('mousemove', (e) => {
        if (isChatModalDragging) {
            // 计算新位置
            const left = e.clientX - chatModalOffsetX;
            const top = e.clientY - chatModalOffsetY;

            // 更新位置（限制在窗口内）
            chatModal.style.left = Math.max(0, Math.min(window.innerWidth - 50, left)) + 'px';
            chatModal.style.top = Math.max(0, Math.min(window.innerHeight - 50, top)) + 'px';
        }
    });

    document.addEventListener('mouseup', (e) => {
        if (isChatModalDragging) {
            isChatModalDragging = false;

            // 恢复正常鼠标样式
            chatModal.style.cursor = '';
            chatModal.style.userSelect = '';

            // 触发位置更新事件
            if (typeof window.updatePositionAfterDrag === 'function') {
                setTimeout(window.updatePositionAfterDrag, 50);
            }
        }
    });

    // 触摸拖动事件
    chatModal.addEventListener('touchstart', (e) => {
        // 如果点击的是关闭按钮或控制元素，不触发拖动
        if (e.target.closest('.close-button') ||
            e.target.tagName === 'INPUT' ||
            e.target.tagName === 'BUTTON' ||
            e.target.closest('.form-group')) return;

        if (e.touches.length === 1) {
            // 阻止默认行为，防止滚动
            e.preventDefault();

            isChatModalDragging = true;
            const touch = e.touches[0];

            // 获取当前位置
            const rect = chatModal.getBoundingClientRect();

            // 计算触摸点相对于模态框的偏移
            chatModalOffsetX = touch.clientX - rect.left;
            chatModalOffsetY = touch.clientY - rect.top;

            // 保存原始样式，用于在拖动后恢复
            chatModal.dataset.originalTransform = chatModal.style.transform || '';
            chatModal.dataset.originalLeft = chatModal.style.left || '';
            chatModal.dataset.originalTop = chatModal.style.top || '';
            chatModal.dataset.originalRight = chatModal.style.right || '';
            chatModal.dataset.originalBottom = chatModal.style.bottom || '';

            // 确保在拖动前清除transform，防止影响拖动
            chatModal.style.transform = 'none';

            // 设置为绝对定位，确保可以通过left/top移动
            chatModal.style.position = 'absolute';
            chatModal.style.bottom = 'auto';
            chatModal.style.right = 'auto';
            chatModal.style.left = rect.left + 'px';
            chatModal.style.top = rect.top + 'px';

            // 增加视觉反馈
            chatModal.style.userSelect = 'none';
        }
    });

    document.addEventListener('touchmove', (e) => {
        if (isChatModalDragging && e.touches.length === 1) {
            const touch = e.touches[0];

            // 计算新位置
            const left = touch.clientX - chatModalOffsetX;
            const top = touch.clientY - chatModalOffsetY;

            // 更新位置（限制在窗口内）
            chatModal.style.left = Math.max(0, Math.min(window.innerWidth - 50, left)) + 'px';
            chatModal.style.top = Math.max(0, Math.min(window.innerHeight - 50, top)) + 'px';

            // 阻止页面滚动
            e.preventDefault();
        }
    });

    document.addEventListener('touchend', (e) => {
        if (isChatModalDragging) {
            isChatModalDragging = false;

            // 恢复正常样式
            chatModal.style.cursor = '';
            chatModal.style.userSelect = '';

            // 触发位置更新事件
            if (typeof window.updatePositionAfterDrag === 'function') {
                setTimeout(window.updatePositionAfterDrag, 50);
            }
        }
    });

    // 为移动设备设置初始位置（左上角）
    function setInitialChatPosition() {
        const isMobile = window.innerWidth <= 768;
        if (isMobile) {
            // 移动设备上的初始位置设置为左上角
            chatModal.style.left = '90px';
            chatModal.style.top = '20px';
            chatModal.style.transform = 'none';
            chatModal.style.bottom = 'auto'; // 清除bottom值，使top生效
            chatModal.style.right = 'auto'; // 清除right值
            chatModal.style.height = '100%'; // 设置高度为100%
        } else {
            // 桌面设备也设置为左上角
            chatModal.style.left = '90px';
            chatModal.style.top = '20px';
            chatModal.style.transform = 'none';
            chatModal.style.bottom = 'auto';
            chatModal.style.right = 'auto';
            chatModal.style.height = '100%'; // 设置高度为100%
        }
    }

    // 页面加载和窗口大小改变时设置位置
    setInitialChatPosition();
    window.addEventListener('resize', setInitialChatPosition);

    // 对话框位置调整功能实现
    function setupChatPositionControls() {
        const chatXPositionSlider = document.getElementById('chatXPositionSlider');
        const chatXPositionValue = document.getElementById('chatXPositionValue');
        const chatYPositionSlider = document.getElementById('chatYPositionSlider');
        const chatYPositionValue = document.getElementById('chatYPositionValue');
        const chatWidthSlider = document.getElementById('chatWidthSlider');
        const chatWidthValue = document.getElementById('chatWidthValue');
        const chatHeightSlider = document.getElementById('chatHeightSlider');
        const chatHeightValue = document.getElementById('chatHeightValue');

        // 快捷位置按钮
        const chatPosLeftTop = document.getElementById('chatPosLeftTop');
        const chatPosCenter = document.getElementById('chatPosCenter');
        const chatPosRightBottom = document.getElementById('chatPosRightBottom');
        const chatPosBottom = document.getElementById('chatPosBottom');
        const chatPosRight = document.getElementById('chatPosRight');

        // 检查控制元素是否存在
        if (!chatXPositionSlider || !chatYPositionSlider) {
            console.log("聊天位置控制元素未找到");
            return;
        }

        // 从localStorage加载对话框位置设置或使用默认值
        function loadChatPositionSettings() {
            // 水平位置
            let savedChatX = localStorage.getItem('chatXPosition');
            if (savedChatX === null) savedChatX = 0; // 默认靠左侧
            chatXPositionSlider.value = savedChatX;
            chatXPositionValue.value = savedChatX;

            // 垂直位置
            let savedChatY = localStorage.getItem('chatYPosition');
            if (savedChatY === null) savedChatY = 0; // 默认左上角位置
            chatYPositionSlider.value = savedChatY;
            chatYPositionValue.value = savedChatY;

            // 宽度
            let savedChatWidth = localStorage.getItem('chatWidth');
            if (savedChatWidth === null) savedChatWidth = 25; // 默认宽度25%
            chatWidthSlider.value = savedChatWidth;
            chatWidthValue.value = savedChatWidth;

            // 高度
            let savedChatHeight = localStorage.getItem('chatHeight');
            if (savedChatHeight === null) savedChatHeight = 100; // 默认高度100%
            chatHeightSlider.value = savedChatHeight;
            chatHeightValue.value = savedChatHeight;

            // 应用设置
            updateChatPosition();

            // 如果是第一次加载（没有保存过位置），设为左上角
            if (savedChatX === null && savedChatY === null) {
                setChatPosition('bottom'); // 使用之前改为左上角的bottom预设
            }
        }

        // 更新对话框位置
        function updateChatPosition() {
            const xPos = parseInt(chatXPositionValue.value);
            const yPos = parseInt(chatYPositionValue.value);
            const width = parseInt(chatWidthValue.value);
            const height = parseInt(chatHeightValue.value);

            // 保存设置到localStorage
            localStorage.setItem('chatXPosition', xPos);
            localStorage.setItem('chatYPosition', yPos);
            localStorage.setItem('chatWidth', width);
            localStorage.setItem('chatHeight', height);

            // 设置对话框样式
            chatModal.style.width = `${width}vw`;
            chatModal.style.height = `${height}vh`;

            // 重置transform，因为有些预设位置可能使用了transform
            chatModal.style.transform = 'none';
            chatModal.style.right = 'auto';
            chatModal.style.bottom = 'auto';

            // 设置位置，考虑百分比
            chatModal.style.left = `${xPos}vw`;
            chatModal.style.top = `${yPos}vh`;

            // 调整位置，考虑对话框自身的尺寸
            chatModal.style.transform = `translate(-${xPos}%, -${yPos}%)`;

            // 同步数字人控制面板的位置和尺寸设置，确保它们与聊天框对应
            synchronizeDigitalHumanControls(xPos, yPos, width, height);
        }

        // 同步数字人控制面板的位置和尺寸设置
        function synchronizeDigitalHumanControls(xPos, yPos, width, height) {
            // 获取数字人面板中的位置和尺寸控制器
            const xOffsetSlider = document.getElementById('xOffsetSlider');
            const xOffsetValue = document.getElementById('xOffsetValue');
            const yOffsetSlider = document.getElementById('yOffsetSlider');
            const yOffsetValue = document.getElementById('yOffsetValue');
            const customWidthSlider = document.getElementById('customWidthSlider');
            const customWidthValue = document.getElementById('customWidthValue');
            const customHeightSlider = document.getElementById('customHeightSlider');
            const customHeightValue = document.getElementById('customHeightValue');

            // 确保所有元素存在
            if (xOffsetSlider && xOffsetValue && yOffsetSlider && yOffsetValue &&
                customWidthSlider && customWidthValue && customHeightSlider && customHeightValue) {

                // 根据窗口大小计算实际像素值
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const pixelX = (xPos / 100) * viewportWidth;
                const pixelY = (yPos / 100) * viewportHeight;

                // 更新数字人位置控制器
                xOffsetValue.value = pixelX;
                xOffsetSlider.value = pixelX;
                yOffsetValue.value = pixelY;
                yOffsetSlider.value = pixelY;

                // 更新数字人尺寸控制器
                const pixelWidth = (width / 100) * viewportWidth;
                const pixelHeight = (height / 100) * viewportHeight;
                customWidthValue.value = pixelWidth;
                customWidthSlider.value = pixelWidth;
                customHeightValue.value = pixelHeight;
                customHeightSlider.value = pixelHeight;
            }
        }

        // 设置预定义位置
        function setChatPosition(position) {
            switch (position) {
                case 'leftTop':
                    chatXPositionValue.value = 0;
                    chatYPositionValue.value = 0;
                    chatModal.style.left = '10px';
                    chatModal.style.top = '10px';
                    chatModal.style.transform = 'none';
                    break;
                case 'center':
                    chatXPositionValue.value = 50;
                    chatYPositionValue.value = 50;
                    chatModal.style.left = '50%';
                    chatModal.style.top = '50%';
                    chatModal.style.transform = 'translate(-50%, -50%)';
                    break;
                case 'rightBottom':
                    chatXPositionValue.value = 100;
                    chatYPositionValue.value = 100;
                    chatModal.style.right = '10px';
                    chatModal.style.bottom = '10px';
                    chatModal.style.left = 'auto';
                    chatModal.style.top = 'auto';
                    chatModal.style.transform = 'none';
                    break;
                case 'bottom':
                    chatXPositionValue.value = 0;
                    chatYPositionValue.value = 0;
                    chatModal.style.left = '90px';
                    chatModal.style.top = '20px';
                    chatModal.style.bottom = 'auto';
                    chatModal.style.transform = 'none';
                    chatModal.style.height = '100%'; // 设置高度为100%
                    break;
                case 'right':
                    chatXPositionValue.value = 100;
                    chatYPositionValue.value = 50;
                    chatModal.style.right = '10px';
                    chatModal.style.top = '50%';
                    chatModal.style.left = 'auto';
                    chatModal.style.transform = 'translateY(-50%)';
                    break;
            }

            // 更新滑块值
            chatXPositionSlider.value = chatXPositionValue.value;
            chatYPositionSlider.value = chatYPositionValue.value;

            // 保存到localStorage
            localStorage.setItem('chatXPosition', chatXPositionValue.value);
            localStorage.setItem('chatYPosition', chatYPositionValue.value);
        }

        // 事件监听
        chatXPositionSlider.addEventListener('input', function () {
            chatXPositionValue.value = this.value;
            updateChatPosition();
        });

        chatXPositionValue.addEventListener('input', function () {
            let value = parseInt(this.value);
            if (isNaN(value)) value = 50;
            if (value < 0) value = 0;
            if (value > 100) value = 100;
            this.value = value;
            chatXPositionSlider.value = value;
            updateChatPosition();
        });

        chatYPositionSlider.addEventListener('input', function () {
            chatYPositionValue.value = this.value;
            updateChatPosition();
        });

        chatYPositionValue.addEventListener('input', function () {
            let value = parseInt(this.value);
            if (isNaN(value)) value = 80;
            if (value < 0) value = 0;
            if (value > 100) value = 100;
            this.value = value;
            chatYPositionSlider.value = value;
            updateChatPosition();
        });

        chatWidthSlider.addEventListener('input', function () {
            chatWidthValue.value = this.value;
            updateChatPosition();
        });

        chatWidthValue.addEventListener('input', function () {
            let value = parseInt(this.value);
            if (isNaN(value)) value = 40;
            if (value < 20) value = 20;
            if (value > 100) value = 100;
            this.value = value;
            chatWidthSlider.value = value;
            updateChatPosition();
        });

        chatHeightSlider.addEventListener('input', function () {
            chatHeightValue.value = this.value;
            updateChatPosition();
        });

        chatHeightValue.addEventListener('input', function () {
            let value = parseInt(this.value);
            if (isNaN(value)) value = 60;
            if (value < 20) value = 20;
            if (value > 100) value = 100;
            this.value = value;
            chatHeightSlider.value = value;
            updateChatPosition();
        });

        // 预设位置按钮事件监听
        if (chatPosLeftTop) {
            chatPosLeftTop.addEventListener('click', function () {
                setChatPosition('leftTop');
            });

            chatPosLeftTop.addEventListener('touchend', function (e) {
                e.preventDefault();
                setChatPosition('leftTop');
            });
        }

        if (chatPosCenter) {
            chatPosCenter.addEventListener('click', function () {
                setChatPosition('center');
            });

            chatPosCenter.addEventListener('touchend', function (e) {
                e.preventDefault();
                setChatPosition('center');
            });
        }

        if (chatPosRightBottom) {
            chatPosRightBottom.addEventListener('click', function () {
                setChatPosition('rightBottom');
            });

            chatPosRightBottom.addEventListener('touchend', function (e) {
                e.preventDefault();
                setChatPosition('rightBottom');
            });
        }

        if (chatPosBottom) {
            chatPosBottom.addEventListener('click', function () {
                setChatPosition('bottom');
            });

            chatPosBottom.addEventListener('touchend', function (e) {
                e.preventDefault();
                setChatPosition('bottom');
            });
        }

        if (chatPosRight) {
            chatPosRight.addEventListener('click', function () {
                setChatPosition('right');
            });

            chatPosRight.addEventListener('touchend', function (e) {
                e.preventDefault();
                setChatPosition('right');
            });
        }

        // 当对话框被拖动后，更新位置设置
        function updatePositionAfterDrag() {
            // 获取对话框的位置
            const rect = chatModal.getBoundingClientRect();

            // 计算相对于视窗的百分比位置
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            const xPercent = Math.round((rect.left / viewportWidth) * 100);
            const yPercent = Math.round((rect.top / viewportHeight) * 100);

            // 更新控制值
            chatXPositionValue.value = xPercent;
            chatXPositionSlider.value = xPercent;
            chatYPositionValue.value = yPercent;
            chatYPositionSlider.value = yPercent;

            // 保存到localStorage
            localStorage.setItem('chatXPosition', xPercent);
            localStorage.setItem('chatYPosition', yPercent);
        }

        // 监听对话框拖动结束事件
        document.addEventListener('mouseup', function (e) {
            if (isChatModalDragging) {
                // 拖动结束后更新位置设置
                setTimeout(updatePositionAfterDrag, 50);
            }
        });

        document.addEventListener('touchend', function (e) {
            if (isChatModalDragging) {
                // 拖动结束后更新位置设置
                setTimeout(updatePositionAfterDrag, 50);
            }
        });

        // 初始化加载对话框位置设置
        loadChatPositionSettings();
    }

    // 当标签页切换到位置调整时设置聊天位置控件
    tabButtons.forEach(button => {
        if (button.getAttribute('data-tab') === 'tab-position') {
            button.addEventListener('click', function () {
                setupChatPositionControls();
            });
        }
    });

    const showChatModalButton = document.getElementById('show-chat-modal');

    let isChatModalOpen = false;
    // 显示对话记录弹窗
    showChatModalButton.addEventListener('click', function () {
        if (isChatModalOpen) {
            chatModal.style.display = 'none';
            isChatModalOpen = false;
        } else {
            chatModal.style.display = 'block';
            // setChatPosition('bottom'); // 使用改名后的left center位置（之前名称为bottom）
            isChatModalOpen = true;
        }
    });

    // 初始显示聊天框
    showChatModalButton.click();

    // 确保拖动元素时不会触发其他事件
    function preventClickDuringDrag() {
        let wasDragging = false;

        // 在拖动开始时设置标志
        function setDragging() {
            wasDragging = true;
            // 300ms后重置标志，允许正常点击
            setTimeout(() => {
                wasDragging = false;
            }, 300);
        }

        // 监听拖动结束事件
        document.addEventListener('mouseup', function (e) {
            if (isChatModalDragging || isDraggingModal) {
                setDragging();
            }
        });

        document.addEventListener('touchend', function (e) {
            if (isChatModalDragging || isDraggingModal) {
                setDragging();
            }
        });

        // 在捕获阶段拦截点击事件
        document.addEventListener('click', function (e) {
            if (wasDragging) {
                e.preventDefault();
                e.stopPropagation();
            }
        }, { capture: true });
    }

    preventClickDuringDrag();

    // 对话框透明度控制
    const chatOpacitySlider = document.getElementById('chatOpacitySlider');
    const chatOpacityValue = document.getElementById('chatOpacityValue');

    function updateChatOpacity(value) {
        // 确保透明度不会太低，避免用户看不到界面
        const numValue = parseInt(value);
        if (numValue < 10) value = 10; // 最小10%不透明度

        const opacity = value / 100;
        chatModal.style.opacity = opacity;

        // 保存设置到localStorage
        localStorage.setItem('chatOpacity', value);
    }

    // 设置默认透明度值
    const DEFAULT_OPACITY = 65;

    // 从localStorage加载保存的透明度设置
    let savedOpacity = localStorage.getItem('chatOpacity');

    // 如果没有保存过设置，或者透明度值过低，使用默认值
    if (savedOpacity === null || parseInt(savedOpacity) < 10) {
        savedOpacity = DEFAULT_OPACITY;
    }

    // 更新UI和实际透明度
    chatOpacitySlider.value = savedOpacity;
    chatOpacityValue.value = savedOpacity;
    updateChatOpacity(savedOpacity);

    // 滑块事件监听
    chatOpacitySlider.addEventListener('input', function () {
        const value = this.value;
        chatOpacityValue.value = value;
        updateChatOpacity(value);
    });

    // 数值输入框事件监听
    chatOpacityValue.addEventListener('input', function () {
        let value = parseInt(this.value);
        if (isNaN(value)) value = 65;
        if (value < 0) value = 0;
        if (value > 100) value = 100;
        this.value = value;
        chatOpacitySlider.value = value;
        updateChatOpacity(value);
    });

    // 定义一个全局的更新位置函数，供拖动后使用
    window.updatePositionAfterDrag = function () {
        const chatXPositionSlider = document.getElementById('chatXPositionSlider');
        const chatXPositionValue = document.getElementById('chatXPositionValue');
        const chatYPositionSlider = document.getElementById('chatYPositionSlider');
        const chatYPositionValue = document.getElementById('chatYPositionValue');
        const chatWidthSlider = document.getElementById('chatWidthSlider');
        const chatWidthValue = document.getElementById('chatWidthValue');
        const chatHeightSlider = document.getElementById('chatHeightSlider');
        const chatHeightValue = document.getElementById('chatHeightValue');

        // 如果控制元素不存在，不执行更新
        if (!chatXPositionSlider || !chatYPositionSlider) {
            return;
        }

        // 获取对话框的位置和尺寸
        const rect = chatModal.getBoundingClientRect();

        // 计算相对于视窗的百分比位置
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        const xPercent = Math.round((rect.left / viewportWidth) * 100);
        const yPercent = Math.round((rect.top / viewportHeight) * 100);
        const widthPercent = Math.round((rect.width / viewportWidth) * 100);
        const heightPercent = Math.round((rect.height / viewportHeight) * 100);

        // 更新控制值
        chatXPositionValue.value = xPercent;
        chatXPositionSlider.value = xPercent;
        chatYPositionValue.value = yPercent;
        chatYPositionSlider.value = yPercent;

        // 更新尺寸控制值
        if (chatWidthSlider && chatWidthValue) {
            chatWidthValue.value = widthPercent;
            chatWidthSlider.value = widthPercent;
        }

        if (chatHeightSlider && chatHeightValue) {
            chatHeightValue.value = heightPercent;
            chatHeightSlider.value = heightPercent;
        }

        // 保存到localStorage
        localStorage.setItem('chatXPosition', xPercent);
        localStorage.setItem('chatYPosition', yPercent);
        localStorage.setItem('chatWidth', widthPercent);
        localStorage.setItem('chatHeight', heightPercent);

        // 同步数字人控制面板的位置和尺寸设置
        synchronizeDigitalHumanControls(xPercent, yPercent, widthPercent, heightPercent);
    };

    // 添加全局变量来跟踪动画状态
    let currentPositionAnimation = null; // 存储当前动画的ID

    /**
     * 数字人定位函数 - 支持多种预设位置和动画效果
     * @param {string} position - 定位类型: 'center', 'centerBottom', 'rightBottom', 'centerMiddle' 等
     * @param {number} animationSpeed - 动画速度比率，1为正常速度，0为无动画，大于1加速
     */
    function positionDigitalHuman(position, animationSpeed = 1) {
        // 取消任何正在进行的动画
        if (currentPositionAnimation) {
            cancelAnimationFrame(currentPositionAnimation);
            currentPositionAnimation = null;
        }

        const canvas = document.getElementById('canvas');
        const mediaDiv = document.getElementById('media');
        const mediaDivRect = mediaDiv.getBoundingClientRect();
        const canvasWidth = parseInt(canvas.style.width || customWidthValue.value);
        const canvasHeight = parseInt(canvas.style.height || customHeightValue.value);

        // 目标位置坐标
        let targetLeft = 0;
        let targetTop = 0;

        // 根据位置类型计算目标坐标
        switch (position) {
            case 'center':
            case 'centerMiddle':
                // 居中
                targetLeft = (mediaDivRect.width - canvasWidth) / 2;
                targetTop = (mediaDivRect.height - canvasHeight) / 2;
                break;
            case 'centerBottom':
                // 底部居中
                targetLeft = (mediaDivRect.width - canvasWidth) / 2;
                targetTop = mediaDivRect.height - canvasHeight;
                break;
            case 'rightBottom':
                // 右下角
                targetLeft = mediaDivRect.width - canvasWidth;
                targetTop = mediaDivRect.height - canvasHeight;
                break;
            case 'leftBottom':
                // 左下角
                targetLeft = 0;
                targetTop = mediaDivRect.height - canvasHeight;
                break;
            case 'topCenter':
                // 顶部居中
                targetLeft = (mediaDivRect.width - canvasWidth) / 2;
                targetTop = 0;
                break;
            case 'topLeft':
                // 左上角
                targetLeft = 0;
                targetTop = 0;
                break;
            case 'topRight':
                // 右上角
                targetLeft = mediaDivRect.width - canvasWidth;
                targetTop = 0;
                break;
            case 'leftMiddle':
                // 左侧居中
                targetLeft = 0;
                targetTop = (mediaDivRect.height - canvasHeight) / 2;
                break;
            case 'rightMiddle':
                // 右侧居中
                targetLeft = mediaDivRect.width - canvasWidth;
                targetTop = (mediaDivRect.height - canvasHeight) / 2;
                break;
            default:
                console.warn('未知的定位类型:', position);
                return;
        }

        // 获取当前位置
        const currentLeft = parseFloat(canvas.style.left) || 0;
        const currentTop = parseFloat(canvas.style.top) || 0;

        // 如果不需要动画，或者目标位置与当前位置一致，直接设置位置
        if (animationSpeed <= 0 ||
            (Math.abs(currentLeft - targetLeft) < 1 && Math.abs(currentTop - targetTop) < 1)) {
            applyPosition(targetLeft, targetTop);
            return;
        }

        // 记录动画开始时间
        console.log(`开始动画：从(${currentLeft},${currentTop}) 到 (${targetLeft},${targetTop}), 速度: ${animationSpeed}`);
        const startPosition = { left: currentLeft, top: currentTop };
        const endPosition = { left: targetLeft, top: targetTop };

        // 使用动画过渡
        const duration = 500 / animationSpeed; // 基础动画时长500ms，根据速度比率调整
        const startTime = performance.now();

        function animate(currentTime) {
            const elapsedTime = currentTime - startTime;
            const progress = Math.min(elapsedTime / duration, 1);

            // 使用缓动函数让动画更自然
            const easeProgress = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);

            // 计算当前动画帧的位置
            const currentAnimLeft = startPosition.left + (endPosition.left - startPosition.left) * easeProgress;
            const currentAnimTop = startPosition.top + (endPosition.top - startPosition.top) * easeProgress;

            // 应用位置
            applyPosition(currentAnimLeft, currentAnimTop);

            // 继续动画或结束
            if (progress < 1) {
                currentPositionAnimation = requestAnimationFrame(animate);
            } else {
                // 动画完成
                currentPositionAnimation = null;
                console.log("动画完成");
            }
        }

        // 启动动画
        currentPositionAnimation = requestAnimationFrame(animate);

        // 辅助函数 - 应用位置变化到UI和控件
        function applyPosition(left, top) {
            // 更新canvas位置
            canvas.style.left = left + 'px';
            canvas.style.top = top + 'px';

            // 更新滑块和输入值
            xOffsetSlider.value = left;
            xOffsetValue.value = left;
            yOffsetSlider.value = top;
            yOffsetValue.value = top;
        }
    }

    // 修改按钮事件处理程序，使用新的定位方法
    positionCenterMiddle.addEventListener('click', function (e) {
        e.stopPropagation(); // 防止事件冒泡
        positionDigitalHuman('centerMiddle', 0.4); // 使用1.5倍速动画
    });

    positionRightBottom.addEventListener('click', function () {
        positionDigitalHuman('rightBottom', 0.4); // 使用1.5倍速动画
    });

    positionCenterBottom.addEventListener('click', function (e) {
        e.stopPropagation(); // 防止事件冒泡
        positionDigitalHuman('centerBottom', 0.4); // 使用1.5倍速动画
    });

    positionLeftBottom.addEventListener('click', function () {
        positionDigitalHuman('leftBottom', 0.4); // 使用1.5倍速动画
    });

    // ========== UI自动隐藏相关 ========== //
    // 控件ID
    const autoHideElements = [
        document.getElementById('show-chat-modal'),
        document.getElementById('settings-button'),
        document.getElementById('voice-recognition-button')
    ];
    const canvasStatus = document.getElementById('canvas-status');
    // UI设置控件
    const uiAutoHideSwitch = document.getElementById('uiAutoHideSwitch');
    const uiAutoHideDelay = document.getElementById('uiAutoHideDelay');
    const uiAutoHideDelayValue = document.getElementById('uiAutoHideDelayValue');

    // 默认参数
    let autoHideEnabled = true;
    let autoHideDelay = 15000;
    let canvasStatusTimer = null;
    let autoHideTimers = {};

    // 读取本地存储
    if (localStorage.getItem('uiAutoHideEnabled') !== null) {
        autoHideEnabled = localStorage.getItem('uiAutoHideEnabled') === 'true';
        uiAutoHideSwitch.checked = autoHideEnabled;
    }
    if (localStorage.getItem('uiAutoHideDelay')) {
        autoHideDelay = parseInt(localStorage.getItem('uiAutoHideDelay'));
        uiAutoHideDelay.value = autoHideDelay;
        uiAutoHideDelayValue.value = autoHideDelay;
    }

    // 联动滑动条和输入框
    uiAutoHideDelay.addEventListener('input', function () {
        uiAutoHideDelayValue.value = this.value;
        autoHideDelay = parseInt(this.value);
        localStorage.setItem('uiAutoHideDelay', autoHideDelay);
        resetAllAutoHideTimers();
    });
    uiAutoHideDelayValue.addEventListener('input', function () {
        let v = parseInt(this.value);
        if (isNaN(v)) v = 15000;
        if (v < 500) v = 500;
        if (v > 15000) v = 15000;
        this.value = v;
        uiAutoHideDelay.value = v;
        autoHideDelay = v;
        localStorage.setItem('uiAutoHideDelay', autoHideDelay);
        resetAllAutoHideTimers();
    });
    uiAutoHideSwitch.addEventListener('change', function () {
        autoHideEnabled = this.checked;
        localStorage.setItem('uiAutoHideEnabled', autoHideEnabled);
        if (autoHideEnabled) {
            resetAllAutoHideTimers();
        } else {
            showAllAutoHideElements();
        }
    });

    // 自动隐藏通用逻辑
    function showAllAutoHideElements() {
        autoHideElements.forEach(el => { if (el) el.style.opacity = 1; });
        if (canvasStatus) canvasStatus.style.opacity = 1;
    }
    function hideAllAutoHideElements() {
        autoHideElements.forEach(el => { if (el) el.style.opacity = 0; });
        if (canvasStatus) canvasStatus.style.opacity = 0;
    }
    function resetAllAutoHideTimers() {
        if (!autoHideEnabled) { showAllAutoHideElements(); return; }
        autoHideElements.forEach(el => resetAutoHideTimer(el));
        resetCanvasStatusTimer();
    }
    // ========== show-chat-modal/settings-button/voice-recognition-button ========== //
    function resetAutoHideTimer(el) {
        if (!autoHideEnabled) { el.style.opacity = 1; return; }
        if (autoHideTimers[el.id]) clearTimeout(autoHideTimers[el.id]);
        el.style.opacity = 1;
        autoHideTimers[el.id] = setTimeout(() => {
            el.style.opacity = 0;
        }, autoHideDelay);
    }
    // 绑定事件：鼠标/触摸进入/点击/移动/按下/抬起都重置
    autoHideElements.forEach(el => {
        ['mouseover', 'mousemove', 'mousedown', 'mouseup', 'touchstart', 'touchmove', 'touchend', 'click'].forEach(evt => {
            el.addEventListener(evt, () => resetAutoHideTimer(el));
        });
    });
    // 页面全局点击/触摸也重置
    document.addEventListener('click', () => {
        // 检查是否在播放媒体
        // const player = document.getElementById('tech-media-player');
        // if (player && player.classList.contains('active')) {
        //     return; // 如果正在播放媒体，不重置UI显示
        // }
        // autoHideElements.forEach(el=>resetAutoHideTimer(el));
    });
    document.addEventListener('touchstart', () => {
        // 检查是否在播放媒体
        const player = document.getElementById('tech-media-player');
        if (player && player.classList.contains('active')) {
            return; // 如果正在播放媒体，不重置UI显示
        }
        autoHideElements.forEach(el => resetAutoHideTimer(el));
    });
    // ========== canvas-status特殊逻辑 ========== //
    function resetCanvasStatusTimer() {
        if (!autoHideEnabled) { if (canvasStatus) canvasStatus.style.opacity = 1; return; }
        if (canvasStatusTimer) clearTimeout(canvasStatusTimer);
        if (canvasStatus) canvasStatus.style.opacity = 1;
        canvasStatusTimer = setTimeout(() => {
            if (canvasStatus) canvasStatus.style.opacity = 0;
        }, autoHideDelay);
    }
    // 只要按下空格或Alt就重置
    document.addEventListener('keydown', function (e) {
        if (e.code === 'Space' || e.key === 'Alt') resetCanvasStatusTimer();
    });
    // 页面加载后立即启动
    if (autoHideEnabled) {
        resetAllAutoHideTimers();
    }
    // ========== 只要显示/切换tab/显示设置面板/显示聊天面板时都重置 ========== //
    const settingsButtonAutoHide = document.getElementById('settings-button');
    if (settingsButtonAutoHide) settingsButtonAutoHide.addEventListener('click', resetAllAutoHideTimers);
    const showChatModalButtonAutoHide = document.getElementById('show-chat-modal');
    if (showChatModalButtonAutoHide) showChatModalButtonAutoHide.addEventListener('click', resetAllAutoHideTimers);
    // 标签页切换
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.addEventListener('click', resetAllAutoHideTimers);
    });


    // 应用背景图片函数
    function applyBackgroundImage(imageUrl) {
        // 获取main-container元素
        const mainContainer = document.querySelector('.main-container');

        if (!mainContainer) {
            console.log('mainContainer', mainContainer);
            console.error('找不到main-container元素');
            return;
        }

        // 输出调试信息
        console.log(`应用背景图片: ${imageUrl}`);

        // 设置背景图

        mainContainer.style.backgroundImage = `url('${imageUrl}')`;

        // 使用现有的背景位置和缩放设置
        updateBackgroundStyle();

        // 保存到localStorage，以便刷新页面后保持
        localStorage.setItem('backgroundImage', imageUrl);
    }

    // ========== 背景图片预设相关 ========== //

    // 动态加载背景图片预设
    async function loadBackgroundPresets() {
        const bgPresetsContainer = document.querySelector('.bg-presets-container');
        if (!bgPresetsContainer) {
            console.error('未找到背景图片预设容器');
            return;
        }

        try {
            // 定义要扫描的图片格式
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
            const bgDirectory = 'static/images/bg/';
            const loadedImages = [];

            // 尝试加载可能存在的图片文件
            // 由于浏览器安全限制，我们无法直接读取目录，所以尝试常见的文件名
            const possibleFiles = [];

            // 生成可能的文件名（1-20的数字文件名）
            for (let i = 1; i <= 20; i++) {
                for (const ext of imageExtensions) {
                    possibleFiles.push(`${i}.${ext}`);
                }
            }

            // 检查每个可能的文件是否存在
            const checkPromises = possibleFiles.map(filename => {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => resolve({ filename, exists: true, url: bgDirectory + filename });
                    img.onerror = () => resolve({ filename, exists: false });
                    img.src = bgDirectory + filename;
                });
            });

            const results = await Promise.all(checkPromises);
            const existingImages = results.filter(result => result.exists);

            if (existingImages.length === 0) {
                console.warn('未找到任何背景图片文件');
                return;
            }

            // 清空现有的预设内容
            bgPresetsContainer.innerHTML = '';

            // 创建新的预设项
            let currentRow = null;
            existingImages.forEach((imageInfo, index) => {
                // 每两个图片创建一行
                if (index % 2 === 0) {
                    currentRow = document.createElement('div');
                    currentRow.className = 'bg-presets-row';
                    bgPresetsContainer.appendChild(currentRow);
                }

                // 创建预设项
                const presetItem = document.createElement('div');
                presetItem.className = 'bg-preset-item';
                presetItem.setAttribute('data-bg-url', imageInfo.url);

                const img = document.createElement('img');
                img.src = imageInfo.url;
                img.alt = `背景图片 ${imageInfo.filename}`;

                presetItem.appendChild(img);
                currentRow.appendChild(presetItem);

                console.log('加载背景图片预设:', imageInfo.url);
            });

            console.log(`成功加载了 ${existingImages.length} 个背景图片预设`);

        } catch (error) {
            console.error('加载背景图片预设时出错:', error);
        }
    }

    // 初始化背景图片预设点击事件
    document.addEventListener('DOMContentLoaded', async function () {
        // 先加载背景图片预设
        await loadBackgroundPresets();
        // 获取所有背景图片预设元素
        const bgPresetItems = document.querySelectorAll('.bg-preset-item');

        // 为每个预设添加点击事件
        bgPresetItems.forEach(item => {
            // 移除之前可能的事件监听器，避免重复
            const clonedItem = item.cloneNode(true);
            item.parentNode.replaceChild(clonedItem, item);

            // 检查图片加载状态
            const imgElement = clonedItem.querySelector('img');
            if (imgElement) {
                const imgSrc = imgElement.getAttribute('src');
                console.log(`检查背景图片预设图片: ${imgSrc}`);
                
                // 检查图片是否已加载
                if (imgElement.complete && imgElement.naturalHeight !== 0) {
                    console.log(`图片已加载: ${imgSrc}`);
                } else {
                    console.log(`图片未加载或加载失败: ${imgSrc}`);
                    
                    // 添加图片加载事件监听器
                    imgElement.addEventListener('load', function() {
                        console.log(`图片加载成功: ${this.src}`);
                    });
                    
                    imgElement.addEventListener('error', function() {
                        console.error(`图片加载失败: ${this.src}`);
                    });
                }
            }

            // 为新元素添加事件监听器
            clonedItem.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();

                // 获取背景图片URL
                const bgUrl = this.getAttribute('data-bg-url');

                // 输出调试信息
                console.log(`点击了背景图片预设: ${bgUrl}`);

                // 修正背景图片路径（如果需要）
                let finalBgUrl = bgUrl;
                if (!bgUrl.startsWith('http') && !bgUrl.startsWith('data:')) {
                    // 确保路径正确，使用正斜杠
                    finalBgUrl = bgUrl.replace(/\\/g, '/');
                }

                // 应用背景图片
                applyBackgroundImage(finalBgUrl);

                // 更新选中状态
                bgPresetItems.forEach(el => el.classList.remove('active'));
                this.classList.add('active');

                // 显示成功提示
                if (typeof showToast === 'function') {
                    showToast('背景图片已应用', 'success');
                } else {
                    console.log('背景图片已应用');
                }

                return false;
            });
        });


        // 检查localStorage是否有保存的背景图片
        const savedBgImage = localStorage.getItem('backgroundImage');
        if (savedBgImage) {
            applyBackgroundImage(savedBgImage);

            // 设置对应预设为选中状态
            bgPresetItems.forEach(item => {
                if (item.getAttribute('data-bg-url') === savedBgImage) {
                    item.classList.add('active');
                }
            });
        }

        // 为文件上传控件添加事件，使其与预设系统协同工作
        const backgroundUpload = document.getElementById('backgroundUpload');
        if (backgroundUpload) {
            backgroundUpload.addEventListener('change', function () {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function (e) {
                        // 应用上传的背景图片
                        applyBackgroundImage(e.target.result);

                        // 清除所有预设的选中状态
                        bgPresetItems.forEach(item => item.classList.remove('active'));

                        // 显示成功提示
                        showToast('自定义背景图片已应用', 'success');
                    };

                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });

    // 立即执行背景图片预设初始化（解决DOMContentLoaded可能已经发生的问题）
    (async function initBackgroundPresets() {
        // 先尝试加载动态背景图片预设
        await loadBackgroundPresets();

        // 获取所有背景图片预设元素
        const bgPresetItems = document.querySelectorAll('.bg-preset-item');

        // 确保找到了预设元素
        if (bgPresetItems.length === 0) {
            console.log('没有找到背景图片预设元素，可能是DOM尚未完全加载');
            // 如果找不到元素，尝试延迟执行
            setTimeout(initBackgroundPresets, 500);
            return;
        }

        console.log(`找到了${bgPresetItems.length}个背景图片预设元素`);

        // 为每个预设添加点击事件
        bgPresetItems.forEach(item => {
            // 移除之前可能的事件监听器，避免重复
            const clonedItem = item.cloneNode(true);
            item.parentNode.replaceChild(clonedItem, item);

            // 为新元素添加事件监听器
            clonedItem.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();

                // 获取背景图片URL
                const bgUrl = this.getAttribute('data-bg-url');

                // 输出调试信息
                console.log(`点击了背景图片预设: ${bgUrl}`);

                // 修正背景图片路径（如果需要）
                let finalBgUrl = bgUrl;
                if (!bgUrl.startsWith('http') && !bgUrl.startsWith('data:')) {
                    // 确保路径正确，使用正斜杠
                    finalBgUrl = bgUrl.replace(/\\/g, '/');
                }

                // 应用背景图片
                applyBackgroundImage(finalBgUrl);

                // 更新选中状态
                const allPresetItems = document.querySelectorAll('.bg-preset-item');
                allPresetItems.forEach(el => el.classList.remove('active'));
                this.classList.add('active');

                // 显示成功提示
                if (typeof showToast === 'function') {
                    showToast('背景图片已应用', 'success');
                } else {
                    console.log('背景图片已应用');
                }

                return false;
            });
        });

        // 检查localStorage是否有保存的背景图片
        const savedBgImage = localStorage.getItem('backgroundImage');
        if (savedBgImage) {
            applyBackgroundImage(savedBgImage);

            // 设置对应预设为选中状态
            bgPresetItems.forEach(item => {
                if (item.getAttribute('data-bg-url') === savedBgImage) {
                    item.classList.add('active');
                }
            });
        }
    })();

    // 触摸拖动事件
    chatModal.addEventListener('touchstart', (e) => {
        // 如果点击的是关闭按钮或控制元素，不触发拖动
        if (e.target.closest('.close-button') ||
            e.target.tagName === 'INPUT' ||
            e.target.tagName === 'BUTTON' ||
            e.target.closest('.form-group')) return;

        // 获取聊天内容容器
        const chatContent = document.getElementById('chat-content');

        // 如果触摸开始在聊天内容区域，则判断为滚动操作而非拖动操作
        if (e.target === chatContent || chatContent.contains(e.target)) {
            // 允许默认的触摸行为（滚动）
            return;
        }

        if (e.touches.length === 1) {
            // 阻止默认行为，防止滚动
            e.preventDefault();

            isChatModalDragging = true;
            const touch = e.touches[0];

            // 获取当前位置
            const rect = chatModal.getBoundingClientRect();

            // 计算触摸点相对于模态框的偏移
            chatModalOffsetX = touch.clientX - rect.left;
            chatModalOffsetY = touch.clientY - rect.top;

            // 保存原始样式，用于在拖动后恢复
            chatModal.dataset.originalTransform = chatModal.style.transform || '';
            chatModal.dataset.originalLeft = chatModal.style.left || '';
            chatModal.dataset.originalTop = chatModal.style.top || '';
            chatModal.dataset.originalRight = chatModal.style.right || '';
            chatModal.dataset.originalBottom = chatModal.style.bottom || '';

            // 确保在拖动前清除transform，防止影响拖动
            chatModal.style.transform = 'none';

            // 设置为绝对定位，确保可以通过left/top移动
            chatModal.style.position = 'absolute';
            chatModal.style.bottom = 'auto';
            chatModal.style.right = 'auto';
            chatModal.style.left = rect.left + 'px';
            chatModal.style.top = rect.top + 'px';

            // 增加视觉反馈
            chatModal.style.userSelect = 'none';
        }
    });

    // 为聊天内容区域添加触摸滚动支持
    const enhanceTouchScrolling = () => {
        const chatContent = document.getElementById('chat-content');
        if (!chatContent) return;

        // 初始触摸位置
        let touchStartY = 0;
        // 当前滚动位置
        let scrollTop = 0;
        // 是否正在滚动
        let isScrolling = false;

        chatContent.addEventListener('touchstart', (e) => {
            if (isChatModalDragging) return; // 如果正在拖动模态框，不处理滚动

            // 记录初始触摸位置
            touchStartY = e.touches[0].clientY;
            // 记录当前滚动位置
            scrollTop = chatContent.scrollTop;
            // 标记正在滚动
            isScrolling = true;

            // 允许继续传播事件，但不阻止默认行为（原生滚动）
        }, { passive: true });

        chatContent.addEventListener('touchmove', (e) => {
            if (!isScrolling || isChatModalDragging) return;

            // 计算触摸移动距离
            const touchDeltaY = touchStartY - e.touches[0].clientY;

            // 设置新的滚动位置
            chatContent.scrollTop = scrollTop + touchDeltaY;

            // 如果滚动到了顶部或底部，允许页面继续滚动
            const isAtTop = chatContent.scrollTop === 0;
            const isAtBottom = chatContent.scrollHeight - chatContent.scrollTop === chatContent.clientHeight;

            if ((isAtTop && touchDeltaY < 0) || (isAtBottom && touchDeltaY > 0)) {
                // 允许页面滚动
            } else {
                // 阻止页面滚动
                e.preventDefault();
            }
        }, { passive: false });
        chatContent.addEventListener('touchend', () => {
            isScrolling = false;
        }, { passive: true });

        chatContent.addEventListener('touchcancel', () => {
            isScrolling = false;
        }, { passive: true });

        // 添加CSS使其在iOS上滚动更流畅
        chatContent.style.webkitOverflowScrolling = 'touch';
        chatContent.style.overflowY = 'auto';
        console.log('已启用聊天内容区域的触摸滚动增强');
    };

    // 初始化触摸滚动支持
    enhanceTouchScrolling();
});

//<!-- 添加语音识别按钮的事件处理脚本 -->

document.addEventListener('DOMContentLoaded', function () {
    const voiceButton = document.getElementById('voice-recognition-button');

    // 获取ASR iframe的引用
    const asrIframe = document.getElementById('asr-iframe');

    // 录音状态标志
    let isRecording = false;

    // 透明度控制变量
    let fadeTimeout;
    const FADE_DELAY = 15000; // 15秒不操作后淡出
    const DEFAULT_OPACITY = 0.45; // 默认透明度

    // 设置初始透明度
    voiceButton.style.opacity = DEFAULT_OPACITY;

    // 淡出函数
    function fadeOut() {
        voiceButton.style.opacity = '0';
        voiceButton.style.transition = 'opacity 1s ease';
    }

    // 重置透明度并设置淡出计时器
    function resetOpacity() {
        voiceButton.style.opacity = DEFAULT_OPACITY;
        voiceButton.style.transition = 'opacity 0.3s ease';

        // 清除之前的计时器
        clearTimeout(fadeTimeout);

        // 设置新的计时器
        fadeTimeout = setTimeout(fadeOut, FADE_DELAY);
    }

    // 初始计时器
    fadeTimeout = setTimeout(fadeOut, FADE_DELAY);

    // 添加触摸或鼠标交互时重置透明度
    voiceButton.addEventListener('mouseover', resetOpacity);
    voiceButton.addEventListener('touchstart', resetOpacity);

    // 按下按钮事件
    function handleButtonDown(event) {
        event.preventDefault(); // 防止默认行为

        if (isRecording) return; // 已经在录音中，不重复处理

        console.log('语音按钮按下，开始录音');
        voiceButton.classList.add('recording'); // 添加录音状态类
        isRecording = true;

        // 重置透明度
        resetOpacity();

        // 调用asr/main.js中的startly方法
        if (asrIframe && asrIframe.contentWindow) {
            try {
                asrIframe.contentWindow.startly();
                console.log('成功调用startly()方法');
            } catch (e) {
                console.error('调用startly()方法失败:', e);
            }
        }
    }

    // 松开按钮事件
    function handleButtonUp(event) {
        event.preventDefault(); // 防止默认行为

        if (!isRecording) return; // 没有在录音，不处理

        console.log('语音按钮松开，停止录音');
        voiceButton.classList.remove('recording'); // 移除录音状态类
        isRecording = false;

        // 重置透明度
        resetOpacity();

        // 调用asr/main.js中的stop方法
        if (asrIframe && asrIframe.contentWindow) {
            try {
                asrIframe.contentWindow.stop();
                console.log('成功调用stop()方法');
            } catch (e) {
                console.error('调用stop()方法失败:', e);
            }
        }
    }

    // 注册鼠标/触摸事件
    // 鼠标事件
    voiceButton.addEventListener('mousedown', handleButtonDown);
    voiceButton.addEventListener('mouseup', handleButtonUp);
    voiceButton.addEventListener('mouseleave', handleButtonUp); // 鼠标移出按钮区域也停止录音

    // 触摸事件（移动设备）
    voiceButton.addEventListener('touchstart', handleButtonDown);
    voiceButton.addEventListener('touchend', handleButtonUp);
    voiceButton.addEventListener('touchcancel', handleButtonUp);

    // 标签页切换监听，在语音识别标签页时调整按钮位置
    const tabButtons = document.querySelectorAll('.tab-button');

    // 初始化页面时设置当前活动标签页的类
    const activeTabButton = document.querySelector('.tab-button.active');
    if (activeTabButton) {
        const tabId = activeTabButton.getAttribute('data-tab');
        document.body.classList.add(`${tabId}-active`);
    } else {
        // 默认设置主页标签为活动状态
        document.body.classList.add('tab-main-active');
    }

    tabButtons.forEach(button => {
        button.addEventListener('click', function () {
            const tabId = this.getAttribute('data-tab');

            // 移除所有标签页相关的类
            document.body.classList.remove('tab-main-active', 'tab-appearance-active', 'tab-position-active', 'tab-recognition-active');

            // 添加当前标签页的类
            document.body.classList.add(`${tabId}-active`);

            // 重置按钮透明度
            resetOpacity();
        });
    });

    // 在页面上任何点击都重置透明度
    document.addEventListener('click', function () {
        // 检查是否在播放媒体
        const player = document.getElementById('tech-media-player');
        if (player && player.classList.contains('active')) {
            return; // 如果正在播放媒体，不重置透明度
        }
        resetOpacity();
    });

    // 在页面上任何触摸都重置透明度
    document.addEventListener('touchstart', function () {
        // 检查是否在播放媒体
        const player = document.getElementById('tech-media-player');
        if (player && player.classList.contains('active')) {
            return; // 如果正在播放媒体，不重置透明度
        }
        resetOpacity();
    });
});

// 获取下拉框和提交按钮元素
const configSelect = document.getElementById('config-select');
const submitButton = document.getElementById('submit-config');

// 获取下拉框选项
async function getConfigOptions() {
    try {
        const response = await fetch(`${window.protocol}://${window.host}/get_config`, {
            method: 'POST',
            body: JSON.stringify({
                sessionid: parseInt(document.getElementById('sessionid').value),
            }),
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error('网络响应异常');
        }
        const data = await response.json();
        console.log(data);

        // 将获取到的数据设置到输入框中
        if (data && data.data) {
            document.getElementById('config-select').value = data.data;
        }
    } catch (error) {
        console.error('获取配置选项时出错:', error);
    }
}

// 处理提交按钮点击事件
async function submitConfig() {
    const text = document.getElementById('config-select').value;
    try {
        const response = await fetch(`${window.protocol}://${window.host}/set_config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: text,
                sessionid: parseInt(document.getElementById('sessionid').value),
            }),

        });
        if (!response.ok) {
            throw new Error('网络响应异常');
        }
        console.log('配置提交成功');
    } catch (error) {
        console.error('提交配置时出错:', error);
    }
    getConfigOptions();
}

// 页面加载完成后获取下拉框选项
// window.addEventListener('load', getConfigOptions);

// 绑定提交按钮点击事件
// submitButton.addEventListener('click', submitConfig);

//======================================================================================
// 保存当前会话ID到localStorage
function saveSessionId() {
    const sessionId = document.getElementById('sessionid').value;
    if (sessionId) {
        localStorage.setItem('asr_session_id', sessionId);
        console.log('已保存会话ID:', sessionId);
    }
}

let originControllerSocket = null;
let clientId = null; // 用于保存客户端ID

// document.getElementById('connect-ws-btn').addEventListener('click', () => {
//     connectToOCServer();
// });

// 断开连接按钮点击事件
// document.getElementById('disconnect-ws-btn').addEventListener('click', () => {
//     if (originControllerSocket && originControllerSocket.readyState === WebSocket.OPEN) {
//         // 请求服务器断开连接
//         cleanupResources({ saveSessionData: false });
//         disconnectFromServer();
//     }
// });

// 页面加载时尝试从localStorage恢复客户端ID
document.addEventListener('DOMContentLoaded', function () {
    try {
        const savedClientId = localStorage.getItem('oc_client_id');
        if (savedClientId) {
            clientId = savedClientId;
            console.log('从localStorage恢复客户端ID:', clientId);
        }
    } catch (e) {
        console.error('读取保存的客户端ID失败:', e);
    }
});

// 添加消息到消息容器
// function addMessage(sender, text, type) {
//     const messageElement = document.createElement('div');
//     messageElement.className = `message ${type}`;
//     messageElement.innerHTML = `<strong>${sender}:</strong> ${text}`;
//     messageContainer.appendChild(messageElement);
//     messageContainer.scrollTop = messageContainer.scrollHeight;
// }


//<!-- 添加背景音乐预设 -->
// 简便的预设音乐添加函数
window.addDefaultMusic = function (path, name = null) {
    // 如果没有提供名称，从路径中提取
    if (!name) {
        const parts = path.split('/');
        name = parts[parts.length - 1];
    }

    console.log(`尝试添加预设音乐: ${name} (${path})`);

    // 检查播放列表中是否已存在该音乐
    if (window.addMusicItem) {
        // 创建音频元素加载音频
        const audio = new Audio(path);

        // 监听元数据加载完成事件
        audio.addEventListener('loadedmetadata', function () {
            // 创建音乐项
            const musicItem = {
                name: name,
                url: path,
                fileInfo: {
                    name: name,
                    type: 'audio/mp3',
                    lastModified: new Date().getTime(),
                    size: 0
                }
            };

            // 添加到播放列表
            window.addMusicItem(musicItem);
            console.log(`成功添加预设音乐: ${name}`);
        });

        // 处理加载错误
        audio.addEventListener('error', function () {
            console.error(`预设音乐加载失败: ${path}`);
        });

        // 加载音频
        audio.load();
    } else {
        console.log('音乐播放器尚未初始化，将在初始化后添加');
        // 添加一个DOMContentLoaded事件监听器
        document.addEventListener('DOMContentLoaded', function checkPlayer() {
            if (window.addMusicItem) {
                window.addDefaultMusic(path, name);
                document.removeEventListener('DOMContentLoaded', checkPlayer);
            }
        });
    }
    return true;
};

// 初始化预设背景音乐
document.addEventListener('DOMContentLoaded', function () {
    // 添加默认背景音乐，只需一行代码
    window.addDefaultMusic('./static/music/周杰伦 - 断了的弦 (伴奏).ogg');
    window.addDefaultMusic('./static/music/周杰伦 - 轨迹 (伴奏).ogg');
    window.addDefaultMusic('./static/music/胡缺六弄 - 晴天 (伴奏).ogg');
    // 后续如需添加更多预设音乐，只需添加类似下面的代码：
    // window.addDefaultMusic('./static/music/另一首音乐.mp3');
    // window.addDefaultMusic('./static/music/custom.mp3', '自定义名称');
});

// document.getElementById('test-human-media').addEventListener('click', function () {
//     console.log('正在发送媒体请求...');
//     fetch(`${window.protocol}://${window.mediaHost}/human`)
//         .then(response => response.json())
//         .then(data => console.log('请求成功:', data))
//         .catch(error => console.error('请求失败:', error));
// });