:root {
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --secondary-color: #f72585;
    --text-primary: #333;
    --text-secondary: #666;
    --background-light: rgba(255, 255, 255, 0.15);
    --background-dark: rgba(248, 249, 250, 0.2);
    --border-color: rgba(224, 224, 224, 0.3);
    --success-color: #2ec4b6;
    --warning-color: #ff9f1c;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --border-radius: 12px;
    --transition: all 0.1s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 0;
    padding: 0;
    background-image: linear-gradient(135deg, rgba(245, 247, 250, 0.1) 0%, rgba(228, 231, 235, 0.1) 100%);
    overflow: hidden;
    color: var(--text-primary);
}

@font-face {
    font-family: 'ChillLongCangKaiMedium';
    src: url('./font-family/ChillLongCangKai_Medium.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}

/* @font-face {
    font-family: 'ChillLongCangKaiExtraBold';
    src: url('./font-family/ChillLongCangKai_ExtraBold.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'ChillLongCangKaiBold';
    src: url('./font-family/ChillLongCangKai_Bold.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'ChillLongCangKaiRegular';
    src: url('./font-family/ChillLongCangKai_Regular.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
} */

/* ASR状态指示器样式 */
#asr-status {
    position: fixed;
    bottom: 20px;
    right: 85px;
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--success-color);
    padding: 8px 15px;
    border-radius: 50px;
    font-size: 14px;
    box-shadow: var(--box-shadow);
    z-index: 1002;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: var(--transition);
}

#asr-status.paused {
    color: var(--warning-color);
}

#asr-status:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.main-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#media {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

#canvas {
    position: absolute;
    z-index: 1000;
    cursor: default;
    transition: var(--transition);
}

#canvas.draggable {
    cursor: move;
    filter: brightness(1.05);
}

.canvas-status {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 13px;
    color: var(--text-secondary);
    background-color: rgba(255, 255, 255, 0.15);
    padding: 8px 16px;
    border-radius: 50px;
    z-index: 1001;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 设置按钮样式 */
#settings-button {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--primary-color);
    box-shadow: var(--box-shadow);
    cursor: pointer;
    z-index: 1002;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
    border: none;
}

#settings-button:hover {
    transform: scale(1.05);
    background-color: var(--primary-hover);
}

#settings-button img {
    width: 28px;
    height: 28px;
    opacity: 0.8;
    transition: var(--transition);
}

#settings-button:hover img {
    opacity: 1;
}

/* 弹窗样式 */
#settings-modal {
    display: none;
    position: absolute;
    top: 20px;
    right: 20px;
    width: 400px;
    background-color: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 1003;
    padding: 0;
    max-height: calc(100vh - 40px);
    overflow: hidden;
    transition: var(--transition);
}

.modal-scroll-container {
    max-height: calc(100vh - 220px);
    overflow-y: auto;
    padding: 0 20px 20px 20px;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
}

.modal-scroll-container:hover {
    scrollbar-width: thin;
    /* Firefox */
    scrollbar-color: var(--border-color) transparent;
}

.modal-scroll-container::-webkit-scrollbar {
    width: 0;
    /* Chrome, Safari, Opera */
    display: none;
}

.modal-scroll-container:hover::-webkit-scrollbar {
    width: 6px;
    display: block;
}

.modal-scroll-container::-webkit-scrollbar-track {
    background: transparent;
}

.modal-scroll-container::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 10px;
}

.chat-modal-header {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    /*background-color: var(--background-light);*/
    z-index: 2;
}

.modal-header {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 2;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    transition: var(--transition);
}

.close-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-primary);
}

.modal-section {
    margin-bottom: 22px;
    padding-bottom: 18px;
    border-bottom: 1px solid var(--border-color);
}

.modal-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 16px 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.section-title svg {
    margin-right: 8px;
    color: var(--primary-color);
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-sizing: border-box;
    resize: vertical;
    font-family: inherit;
    transition: var(--transition);
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* 数字输入框样式 */
input[type="number"] {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(224, 224, 224, 0.4);
    border-radius: 4px;
    padding: 5px;
    color: var(--text-primary);
    font-size: 14px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

input[type="number"]:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.15);
}

/* 文本输入框样式 */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="url"] {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(224, 224, 224, 0.4);
    border-radius: 4px;
    padding: 8px 12px;
    color: var(--text-primary);
    font-size: 14px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="url"]:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.15);
}

.range-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.range-container input[type="range"] {
    flex: 1;
}

.range-value {
    min-width: 40px;
    padding: 4px 8px;
    background-color: rgba(248, 249, 250, 0.2);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    text-align: center;
    font-size: 13px;
    font-weight: 500;
}

button {
    padding: 10px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    margin-right: 8px;
    margin-bottom: 8px;
    transition: var(--transition);
}

button:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* 开关样式 */
.switch-container {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

}

.switch-container label {
    margin: 0 10px 0 0;
    cursor: pointer;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: .4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: var(--primary-color);
}

input:focus+.slider {
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

input:checked+.slider:before {
    transform: translateX(20px);
}

/* 文件上传样式 */
.file-upload {
    position: relative;
    overflow: hidden;
    margin-top: 8px;
    width: 100%;
}

.file-upload-label {
    display: block;
    padding: 12px;
    background-color: rgba(248, 249, 250, 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-primary);
    border: 1px dashed rgba(224, 224, 224, 0.4);
    border-radius: 8px;
    cursor: pointer;
    text-align: center;
    transition: var(--transition);
}

.file-upload-label:hover {
    background-color: rgba(240, 240, 240, 0.2);
    border-color: rgba(224, 224, 224, 0.6);
}

.file-upload-label svg {
    margin-right: 8px;
    vertical-align: middle;
}

.file-upload input[type="file"] {
    position: absolute;
    font-size: 100px;
    opacity: 0;
    right: 0;
    top: 0;
    cursor: pointer;
}

/* 按钮变体 */
.btn-secondary {
    background-color: rgba(248, 249, 250, 0.2);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: rgba(233, 236, 239, 0.3);
}

.btn-success {
    background-color: var(--success-color);
}

.btn-success:hover {
    background-color: #25b0a3;
}

.btn-warning {
    background-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #f59000;
}

/* 颜色选择器样式 */
input[type="color"] {
    -webkit-appearance: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
}

input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 8px;
}

.color-preview {
    display: flex;
    align-items: center;
}

.color-value {
    margin-left: 12px;
    font-family: monospace;
    font-size: 14px;
    padding: 4px 8px;
    background-color: rgba(248, 249, 250, 0.2);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}


.vision-container {
    background-color: rgba(248, 249, 250, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    /*overflow: hidden;*/
    height: auto;
    /* Change from 100% to auto */
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.vision-container:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.1);
}

/* 视觉感知摄像头样式 */
.vision-camera {
    width: 100%;
    max-width: 320px;
    height: auto;
    border-radius: 12px;
    border: 2px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.vision-camera:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* 摄像头控制按钮容器 */
.vision-controls {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

/* 摄像头控制按钮 */
.vision-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 100px;
    justify-content: center;
}

.vision-btn-start {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.vision-btn-start:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    transform: translateY(-1px);
}

.vision-btn-stop {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.vision-btn-stop:hover {
    background: linear-gradient(135deg, #d32f2f, #c62828);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
    transform: translateY(-1px);
}

.vision-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}



/* 响应式设计 */
@media (max-width: 480px) {
    .vision-container {
        padding: 15px;
    }

    .vision-controls {
        flex-direction: column;
        align-items: center;
    }

    .vision-btn {
        width: 100%;
        max-width: 200px;
    }
}

/* ASR iframe */
.asr-container {
    background-color: rgba(248, 249, 250, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    /*overflow: hidden;*/
    height: auto;
    /* Change from 100% to auto */
}

#asr-iframe {
    border: none;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    height: auto;
    /* Allow height to adjust automatically */
    min-height: 500px;
    /* Set a minimum height if needed */
}

/* 响应式样式 */
@media (max-width: 768px) {
    #settings-modal {
        width: calc(100% - 40px);
        right: 20px;
        /*max-height: 80vh;*/
    }

    #settings-button {
        width: 48px;
        height: 48px;
    }

    #settings-button img {
        width: 24px;
        height: 24px;
    }

    .canvas-status {
        display: none;
    }
}

/* 标签页样式 */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
    overflow-x: auto;
    scrollbar-width: none;
    white-space: nowrap;
}

.tabs::-webkit-scrollbar {
    display: none;
}

.tab-button {
    padding: 10px 16px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin: 0;
    transition: var(--transition);
}

.tab-button:hover {
    color: var(--text-primary);
    background: none;
    transform: none;
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 隐藏元素 */
#audio {
    display: none;
}

/* 遮罩层 */
#modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /*background-color: rgba(0, 0, 0, 0.2);*/
    /*取消遮罩层的模糊效果*/
    /*backdrop-filter: blur(2px);*/
    z-index: 1000;
    transition: var(--transition);
}

.status-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--success-color);
    padding: 8px 15px;
    border-radius: 50px;
    font-size: 14px;
    box-shadow: var(--box-shadow);
    z-index: 1002;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: var(--transition);
}

.status-indicator.paused {
    color: var(--warning-color);
}

.status-indicator:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

#chat-modal {
    display: none;
    position: absolute;
    top: 20px;
    left: 90px;
    /* 修改为90px，留出足够空间不覆盖show-chat-modal按钮 */
    max-width: 25%;
    /* 修改为25% */
    background-color: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius);
    z-index: 1002;
    padding: 0;
    max-height: 90%;
    /* 修改为100% */
    overflow: hidden;
    /*transition: var(--transition);*/
}

#chat-modal:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* 覆盖 #chat-modal 内部的 .modal-scroll-container 的 padding 属性 */
#chat-modal .modal-scroll-container {
    padding: 20px;
    /* 这里设置你想要的 padding 值 */
    /* opacity: 0.65; */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* 增加iOS滚动惯性 */
    overscroll-behavior: contain;
    /* 防止滚动穿透 */
}

/* 手机端对话框底部居中样式 */
@media (max-width: 768px) {
    #chat-modal {
        left: 0 !important;
        right: 0 !important;
        top: auto !important;
        bottom: 120px !important;
        width: 100vw !important;
        max-width: 100vw !important;
        height: 160px !important;
        max-height: 20vh !important;
        border-radius: 16px 16px 0 0 !important;
    }

    #chat-modal .modal-scroll-container {
        /* 为移动设备添加特殊样式 */
        height: 100%;
        overflow-y: scroll;
        /* 强制显示滚动 */
        -webkit-overflow-scrolling: touch;
        touch-action: pan-y;
        /* 允许Y轴滚动 */
        scrollbar-width: thin;
    }

    /* 滚动提示 */
    #chat-modal::after {
        content: "";
        position: absolute;
        bottom: 5px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background-color: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        border-radius: 2px;
        z-index: 1010;
    }
}

#show-chat-modal {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--primary-color);
    box-shadow: var(--box-shadow);
    cursor: pointer;
    z-index: 1003;
    transition: all 0.3s ease;
}

#test-chat-media {
    position: fixed;
    top: 150px;
    left: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #42B983;
    color: white;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
    border: none;
    z-index: 1002;
    /* 设置在数字人canvas(1000)之上，但在科技感媒体播放器(1005)之下 */
    /* display: none; */
}

/* 添加媒体播放测试按钮样式 */
#test-media-player-btn {
    position: fixed;
    top: 210px;
    left: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3498db;
    color: white;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    z-index: 1002;
    /* 设置在数字人canvas(1000)之上，但在科技感媒体播放器(1005)之下 */
}

#test-media-player-btn:hover {
    transform: scale(1.1);
    background-color: #2980b9;
}

#test-media-player-btn svg {
    width: 20px;
    height: 20px;
}

#show-chat-modal:hover {
    transform: scale(1.05);
    background-color: var(--primary-hover);
}

.chat-item {
    /* 每个聊天消息的样式 */
    /*border: 1px solid #dcdcdc;*/
    border-radius: 8px;
    /*padding: 10px;*/
    margin-bottom: 10px;
    /*background-color: #ffffff;*/
    /*box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);*/
    transition: all 0.3s ease;
    /*white-space: nowrap;*/
    overflow: visible;
    word-break: break-all;
    overflow-wrap: break-word;
}

.chat-item .div {
    /*white-space: nowrap;*/
    overflow: visible;
    word-break: break-all;
    overflow-wrap: break-word;
}

.chat-item:hover {
    /* 鼠标悬停时的样式 */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
    transform: translateY(-2px);
}

#test-chat-media:hover {
    transform: scale(1.1);
    background-color: #35A171;
}

#test-chat-media svg,
#show-chat-modal svg {
    width: 20px;
    height: 20px;
}

#test-human-media:hover {
    transform: scale(1.1);
    background-color: #35A171;
}

#test-human-media svg {
    width: 20px;
    height: 20px;
}

/* 添加语音识别圆形按钮样式 */
#voice-recognition-button {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #4361ee;
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 1003;
    outline: none;
    display: none;
}

#voice-recognition-button:active,
#voice-recognition-button.recording {
    background-color: #f72585;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transform: translateX(-50%) scale(0.95);
}

#voice-recognition-button svg {
    width: 30px;
    height: 30px;
}

/* 添加录音动画效果 */
#voice-recognition-button.recording::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(247, 37, 133, 0.3);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }

    70% {
        transform: scale(1.2);
        opacity: 0;
    }

    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* 移动设备适配 */
@media (max-width: 768px) {
    #voice-recognition-button {
        width: 80px;
        height: 80px;
        bottom: 40px;
        display: flex;
    }

    #voice-recognition-button svg {
        width: 40px;
        height: 40px;
    }

    /* 在语音识别标签页时调整按钮位置 */
    .tab-recognition-active #voice-recognition-button {
        bottom: 20px;
    }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
    #voice-recognition-button {
        width: 70px;
        height: 70px;
        bottom: 35px;
    }

    #voice-recognition-button svg {
        width: 35px;
        height: 120px;
    }
}

/* 桌面设备适配 */
@media (min-width: 1025px) {
    #voice-recognition-button {
        width: 60px;
        height: 60px;
        bottom: 30px;
        right: 30px;
        left: auto;
        transform: none;
    }

    #voice-recognition-button:active,
    #voice-recognition-button.recording {
        transform: scale(0.95);
    }
}

/* 手机端对话框底部居中样式 */
@media (max-width: 768px) {
    #chat-modal {
        left: 0 !important;
        right: 0 !important;
        top: auto !important;
        bottom: 120px !important;
        width: 100vw !important;
        max-width: 100vw !important;
        height: 160px !important;
        max-height: 20vh !important;
        border-radius: 16px 16px 0 0 !important;
        padding-bottom: env(safe-area-inset-bottom, 0);
        /* 兼容iPhone X底部安全区 */
        box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.12);

    }

    #chat-modal .modal-scroll-container {
        padding: 10px 8px 8px 8px;
        max-height: 100%;
    }
}

#info_div {
    margin-bottom: 16px;
    font-weight: 500;
    color: var(--text-primary);
    text-align: center;
    padding: 8px;
    border-radius: 8px;
    background-color: rgba(67, 97, 238, 0.1);
}

/* 自定义播放列表样式 */
.custom-playlist-wrapper {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    max-height: 200px;
    position: relative;
    transition: box-shadow 0.2s ease;
}

.custom-playlist-wrapper:focus-within {
    box-shadow: 0 0 0 2px rgba(58, 86, 212, 0.3);
}

.custom-playlist-scroll {
    overflow-y: auto;
    max-height: 200px;
    padding: 0;
    -webkit-overflow-scrolling: touch;
    /* 提升iOS滚动体验 */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

/* 自定义滚动条样式 */
.custom-playlist-scroll::-webkit-scrollbar {
    width: 6px;
}

.custom-playlist-scroll::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 3px;
}

.custom-playlist-scroll::-webkit-scrollbar-track {
    background-color: transparent;
}

.custom-playlist {
    list-style: none;
    margin: 0;
    padding: 0;
}

.playlist-item {
    padding: 10px 12px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
    user-select: none;
    /* 防止文本被选中 */
    -webkit-tap-highlight-color: transparent;
    /* 移除iOS点击高亮 */
    position: relative;
}

.playlist-item:last-child {
    border-bottom: none;
}

.playlist-item:active {
    background-color: rgba(58, 86, 212, 0.3);
}

.playlist-item.selected {
    background-color: rgba(58, 86, 212, 0.2);
}

.playlist-item.playing {
    font-weight: bold;
    color: var(--primary-color);
    background-color: rgba(58, 86, 212, 0.1);
}

.playlist-item.playing::before {
    content: '▶';
    margin-right: 8px;
    font-size: 10px;
}

.track-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 触摸设备样式优化 */
@media (pointer: coarse) {
    .playlist-item {
        padding: 12px 15px;
        /* 更大的点击区域 */
        min-height: 24px;
        /* 确保有足够的高度 */
    }

    .playlist-controls .btn-small {
        padding: 10px 15px;
        /* 更大的按钮 */
    }
}

/* 播放列表控制按钮样式 */
.playlist-control-btn {
    white-space: nowrap;
    /* 防止文本换行 */
    overflow: hidden;
    /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    /* 文本溢出时显示省略号 */
    min-width: 0;
    /* 允许按钮缩小至比内容小 */
    padding: 8px 10px;
    /* 统一内边距 */
}

/* 输入框样式 */
.input-field {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    color: var(--text-primary);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.input-field:focus {
    border-color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(58, 86, 212, 0.2);
    outline: none;
}

.input-field[readonly] {
    background-color: rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    cursor: default;
}

/* 按钮文本切换 */
.btn-text-short {
    display: none;
    /* 默认隐藏短文本 */
}

/* 窄屏幕适配 */
@media (max-width: 480px) {
    .playlist-controls {
        gap: 5px;
        /* 减小按钮间距 */
    }

    .playlist-control-btn {
        padding: 8px 5px;
        /* 减小内边距 */
    }
}

/* 超窄屏幕适配 */
@media (max-width: 360px) {
    .btn-text-normal {
        display: none;
        /* 隐藏正常文本 */
    }

    .btn-text-short {
        display: inline;
        /* 显示短文本 */
    }

    .playlist-control-btn {
        padding: 8px 3px;
        /* 进一步减小内边距 */
    }
}

/* 待恢复的播放列表项目 */
.playlist-item.pending-restore {
    background-color: rgba(225, 135, 9, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-left: 3px solid #e18709;
    padding-left: 9px;
    /* 12px - 3px(边框) */
}

.playlist-item.pending-restore .track-name {
    color: var(--text-secondary);
}

.pending-icon {
    margin-left: 8px;
    opacity: 0.7;
    color: #e18709;
}

/* 窄屏幕适配 */
@media (max-width: 480px) {
    .playlist-controls {
        gap: 5px;
        /* 减小按钮间距 */
    }

    .playlist-control-btn {
        padding: 8px 5px;
        /* 减小内边距 */
    }
}

/* 响应式按钮文本样式 */
@media screen and (max-width: 576px) {
    #browse-folder-btn .full-text {
        display: none;
    }

    #browse-folder-btn .short-text {
        display: inline !important;
    }
}

/* 科技感媒体播放器样式 */
.tech-media-player {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1005;
    background-color: transparent;
    backdrop-filter: none;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.5s ease;
}

.tech-media-player.active {
    opacity: 1;
    pointer-events: auto;
}

.tech-media-player-inner {
    position: relative;
    width: 60%;
    min-width: 300px;
    max-width: 1200px;
    transform: scale(0.5);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 0 30px rgba(0, 180, 250, 0.5);
}

.tech-media-player.active .tech-media-player-inner {
    transform: scale(1);
}

.tech-media-content {
    width: 100%;
    height: 100%;
    background-color: rgba(20, 30, 48, 0.8);
    border: 2px solid rgba(0, 180, 250, 0.7);
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.tech-media-content img,
.tech-media-content video {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.tech-player-border {
    position: absolute;
    width: 20px;
    height: 20px;
    border-color: rgb(0, 195, 255);
    border-style: solid;
    border-width: 0;
}

.tech-player-border.top-left {
    top: -3px;
    left: -3px;
    border-top-width: 3px;
    border-left-width: 3px;
}

.tech-player-border.top-right {
    top: -3px;
    right: -3px;
    border-top-width: 3px;
    border-right-width: 3px;
}

.tech-player-border.bottom-left {
    bottom: -3px;
    left: -3px;
    border-bottom-width: 3px;
    border-left-width: 3px;
}

.tech-player-border.bottom-right {
    bottom: -3px;
    right: -3px;
    border-bottom-width: 3px;
    border-right-width: 3px;
}

.tech-player-close {
    position: absolute;
    top: 10px;
    right: 0px;
    width: 20px;
    height: 20px;
    background-color: transparent;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
    padding: 0;
}

.tech-player-close:hover {
    transform: scale(1.2);
    color: rgb(255, 70, 70);
}

.tech-player-close svg {
    width: 24px;
    height: 24px;
    /* stroke: currentColor; */
    stroke-width: 2;
}

/* 用于数字人位置移动的动画 */
@keyframes moveDigitalHuman {
    0% {
        transform: translate(0, 0) scale(1);
    }

    100% {
        transform: translate(var(--move-x), var(--move-y)) scale(var(--scale));
    }
}

.dh-animating {
    animation: moveDigitalHuman 0.5s forwards ease-in-out;
}

/* 对科技感媒体播放器关闭按钮的增强样式 */
.tech-player-close {
    outline: none !important;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.tech-player-close:hover,
.tech-player-close:focus,
.tech-player-close:active {
    outline: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

/* 移除button元素的默认外观 */
/* button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
} */

.bg-presets-row {
    display: flex;
    margin-bottom: 10px;
    gap: 10px;
}

.bg-preset-item {
    flex: 1;
    position: relative;
    padding-top: 28.125%;
    /* 16:9比例 = 9/16 * 50% */
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s;
}

.bg-preset-item:hover {
    border-color: #4361ee;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.bg-preset-item.active {
    border-color: #4361ee;
}

.bg-preset-item img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bg-preset-item video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}



/* 动画按钮组样式 */
.animation-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.animation-button {
    padding: 10px 16px;
    background-color: rgba(248, 249, 250, 0.2);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: 100px;
    position: relative;
    overflow: hidden;
}

.animation-button:hover {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.2);
}

.animation-button:active {
    transform: translateY(0);
}

.animation-button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.animation-button.active:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4);
}

/* 添加图标样式 */
.animation-button svg {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    stroke: currentColor;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
}

/* 添加涟漪效果 */
.animation-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.animation-button:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}