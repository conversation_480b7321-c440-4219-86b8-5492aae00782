var rec_text = ""; // for online rec asr result
var offline_text = ""; // for offline rec asr result
var isfilemode = false; // if it is in file mode
var file_data_array; // array to save file data
var totalsend = 0;

let sampleBuf = new Int16Array();

// 添加录音状态枚举
const RecordingStateEnum = {
  STOPPED: "stopped",
  STARTING: "starting",
  RECORDING: "recording",
};

// 替换原来的talking布尔值
let recordingState = RecordingStateEnum.STOPPED;

const btnButtonCall = document.querySelector("#btnButtonCall");
const image_call = "url(./images/phone_call.png)"; // 电话呼叫的图片URL
const image_down = "url(./images/phone_down.png)"; // 电话挂断的图片URL

const wsconnecter = new WebSocketConnectMethod({
  msgHandle: getJsonMessage,
  stateHandle: getConnState,
});

let isRec = false;
// 录音; 定义录音对象,wav格式
const rec = Recorder({
  type: "pcm",
  bitRate: 16,
  sampleRate: 16000,
  bufferSize: 4096, // 添加较小的缓冲区大小以减少延迟
  onProcess: recProcess,
});

// 在文件顶部添加关键词数组
const TRIGGER_KEYWORDS = ["小幸运", "小。幸运"];

// 添加控制音频静音的函数
function toggleMute(isMuted) {
  const videoElement = document.getElementById("rtc_media_player");
  videoElement.muted = isMuted;

  // 如果取消静音，尝试播放视频
  if (!isMuted) {
    const playPromise = videoElement.play();
    if (playPromise !== undefined) {
      playPromise.catch((error) => {
        console.error("播放失败:", error);
        // 如果取消静音导致播放失败，可能是浏览器策略限制
        // 可以考虑添加用户交互引导
      });
    }
  }

  console.log(`音频和视频已${isMuted ? "静音" : "取消静音"}`);
}

$(document).ready(function () {
  const sessionid = Math.floor(100000 + Math.random() * 900000).toString();
  document.getElementById("sessionid").value = sessionid;

  var sdk = null;
  var startPlay = function () {
    // 确保视频元素可见
    $("#rtc_media_player").show();

    // 先设置静音，确保能自动播放（符合浏览器策略）
    const videoElement = document.getElementById("rtc_media_player");
    videoElement.muted = true;

    // Close PC when user replay.
    if (sdk) {
      sdk.close();
    }
    sdk = new SrsRtcWhipWhepAsync();

    $("#rtc_media_player").prop("srcObject", sdk.stream);

    var host = window.location.hostname;

    var url =
      "http://" +
      host +
      ":1985/rtc/v1/whep/?app=live&stream=livestream" +
      sessionid;
    console.log("url: ", url);
    sdk
      .play(url)
      .then(function (session) {
        // 添加自动播放检测和处理
        const playPromise = videoElement.play();

        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              console.log("自动播放成功");
              // 添加提示信息，告诉用户点击页面启用声音
              const unmuteTip = document.createElement("div");
              unmuteTip.innerHTML = "点击页面启用声音";
              unmuteTip.style.position = "absolute";
              unmuteTip.style.top = "20px";
              unmuteTip.style.left = "50%";
              unmuteTip.style.transform = "translateX(-50%)";
              unmuteTip.style.background = "rgba(0,0,0,0.5)";
              unmuteTip.style.color = "white";
              unmuteTip.style.padding = "10px 20px";
              unmuteTip.style.borderRadius = "5px";
              unmuteTip.style.zIndex = "1000";
              unmuteTip.style.transition = "opacity 0.5s";
              document.body.appendChild(unmuteTip);

              // 用户点击页面后移除提示
              document.addEventListener(
                "click",
                function removeTip() {
                  unmuteTip.style.opacity = "0";
                  setTimeout(() => {
                    if (unmuteTip.parentNode) {
                      unmuteTip.parentNode.removeChild(unmuteTip);
                    }
                  }, 500);
                  document.removeEventListener("click", removeTip);
                },
                { once: true }
              );
            })
            .catch((error) => {
              console.error("自动播放失败:", error);
              // 自动播放失败时，显示提示或添加点击事件处理
              alert("请点击屏幕开始播放视频");

              // 添加点击事件来启动播放
              document.addEventListener(
                "click",
                function startPlayOnce() {
                  videoElement.play();
                  document.removeEventListener("click", startPlayOnce);
                },
                { once: true }
              );
            });
        }
      })
      .catch(function (reason) {
        sdk.close();
        $("#rtc_media_player").hide();
        console.error(reason);
      });
  };

  startPlay();

  // 调用run接口初始化会话，确保视频播放就绪
  runWithSessionId()
    .then((result) => {
      console.log("视频会话已初始化");
    })
    .catch((error) => {
      console.error("视频会话初始化失败:", error);
    });

  startSpeakingCheck(); // 启动说话状态检查

  // 添加全页面点击事件处理，在用户第一次交互时取消静音
  let hasInteracted = false;
  document.addEventListener("click", function handleFirstInteraction(event) {
    if (!hasInteracted) {
      hasInteracted = true;
      const videoElement = document.getElementById("rtc_media_player");
      // 取消静音
      videoElement.muted = false;
      console.log("用户已交互，已取消静音");
      // 移除事件监听器，避免重复处理
      document.removeEventListener("click", handleFirstInteraction);
    }
  });

  // 在页面加载时保持静音状态
  // 不要在这里取消静音，会违反自动播放政策
  // 改为在用户交互后再取消静音
  // toggleMute(false);

  // 设置初始按钮背景图片
  btnButtonCall.style.backgroundImage = image_call;

  // 为"呼叫"按钮添加点击事件监听器
  btnButtonCall.addEventListener("click", function (event) {
    if (btnButtonCall.style.backgroundImage.includes("phone_call")) {
      const videoElement = document.getElementById("rtc_media_player");

      // 标记已交互
      hasInteracted = true;

      // 确保视频显示
      videoElement.style.display = "block";

      // 取消静音（用户交互后，可以安全地取消静音）
      videoElement.muted = false;

      // 确保开始播放
      videoElement.play().catch((err) => {
        console.error("点击后播放失败:", err);
      });

      // 修改界面状态
      btnButtonCall.style.backgroundImage = image_down; // 改变按钮图片为挂断电话

      // 开始录音
      start_record();
    } else {
      btnButtonCall.style.backgroundImage = image_call; // 改变按钮图片为呼叫电话
      stop(); // 停止录音

      // 重新设置静音
      const videoElement = document.getElementById("rtc_media_player");
      videoElement.muted = true;

      // 重置交互状态，下次需要再次点击
      hasInteracted = false;
    }
  });
});

function start_record() {
  if (recordingState !== RecordingStateEnum.STOPPED) {
    console.log(`录音当前状态: ${recordingState}, 等待1秒后重试`);
    sleep(() => start_record(), 1000);
    return;
  }

  try {
    recordingState = RecordingStateEnum.STARTING;
    const startResult = start();
    if (startResult === 1) {
      record();
      console.log("录音开始成功");
    } else {
      console.log("录音启动失败");
      stop();
    }
  } catch (error) {
    console.error("录音启动出错:", error);
    stop();
  }
}

function getJsonMessage(jsonMsg) {
  const data = JSON.parse(jsonMsg.data);
  const rectxt = String(data.text);
  // console.log("--------------------data: " + JSON.stringify(data));
  console.log("--------------------org_text: " + rectxt);

  // 检查是否包含任意一个关键词
  const hasKeyword = TRIGGER_KEYWORDS.some((keyword) =>
    rectxt.includes(keyword)
  );
  if (!hasKeyword) return;

  const asrmodel = data.mode;
  const is_final = data.is_final;

  // 使用switch语句优化条件判断
  switch (asrmodel) {
    case "2pass-offline":
      const cleanText = rectxt.replace(/ +/g, "");
      offline_text += cleanText + "\n";
      rec_text = offline_text;
      message_send(rectxt);
      console.log("==================2pass-offline: " + rectxt);
      break;
    case "offline":
    // const cleanText = rectxt.replace(/ +/g, "");
    // offline_text += cleanText + "\n";
    // rec_text = offline_text;
    // console.log("==================offline: " + rectxt);
    // message_send(rectxt);
    // break;
    default:
      rec_text += rectxt;
  }

  // if (is_final) {
  //   wsconnecter.wsStop();
  // }
}

function getConnState(connState) {
  // console.log('connState------------------------------->',connState);
  if (connState === 0) {
    //on open
    if (isfilemode) {
      start_file_send();
    }
  } else if (connState === 1) {
    //stop();
  } else if (connState === 2) {
    stop();
    // console.log("connecttion error");
    alert(
      "连接地址" +
        document.getElementById("wssip").value +
        "失败,请检查asr地址和端口。或试试界面上手动授权，再连接。"
    );
  }
}

function start_file_send() {
  sampleBuf = new Uint8Array(file_data_array);

  var chunk_size = 960; // for asr chunk_size [5, 10, 5]

  while (sampleBuf.length >= chunk_size) {
    sendBuf = sampleBuf.slice(0, chunk_size);
    totalsend = totalsend + sampleBuf.length;
    sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length);
    wsconnecter.wsSend(sendBuf);
  }

  stop();
}

function sleep(callback, delay) {
  setTimeout(() => {
    callback();
  }, delay);
}

function record() {
  rec.open(
    function () {
      try {
        rec.start();
        recordingState = RecordingStateEnum.RECORDING;
        console.log("录音已开始");
      } catch (error) {
        console.error("录音启动失败:", error);
        stop();
      }
    },
    function (error) {
      console.error("录音设备打开失败:", error);
      stop();
    }
  );
}

function start() {
  if (recordingState !== RecordingStateEnum.STARTING) {
    console.log(`录音状态错误: ${recordingState}`);
    return 0;
  }

  clear();

  const ret = wsconnecter.wsStart();
  if (ret === 1) {
    isRec = true;
    return 1;
  } else {
    console.log("WebSocket连接失败");
    return 0;
  }
}

function stop() {
  if (recordingState === RecordingStateEnum.STOPPED) {
    console.log("录音已经停止");
    return;
  }

  try {
    isRec = false;
    rec.stop();
    console.log("录音已停止");
  } catch (error) {
    console.error("停止录音时出错:", error);
  } finally {
    recordingState = RecordingStateEnum.STOPPED;
    isRec = false;
  }
}

function clear() {
  rec_text = "";
  offline_text = "";
}

function recProcess(
  buffer,
  powerLevel,
  bufferDuration,
  bufferSampleRate,
  newBufferIdx,
  asyncEnd
) {
  if (!isRec) return;

  const data_48k = buffer[buffer.length - 1];
  const array_48k = [data_48k];
  const data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;

  // 使用更高效的数组操作
  sampleBuf = new Int16Array([...sampleBuf, ...data_16k]);

  const chunk_size = 960;
  while (sampleBuf.length >= chunk_size) {
    const sendBuf = sampleBuf.slice(0, chunk_size);
    wsconnecter.wsSend(sendBuf);
    sampleBuf = sampleBuf.slice(chunk_size);
  }
}

function getHotwords() {
  var jsonresult = { 阿里巴巴: 20, 钱心怡: 40 };
  return JSON.stringify(jsonresult);
}

function getAsrMode() {
  return "2pass";
}

function getUseITN() {
  return true;
}

function message_get() {
  var message = $("#message").val();
  console.log("Sending: " + message);
  console.log("sessionid: ", document.getElementById("sessionid").value);

  message_send(message);

  $("#message").val("");
}

function message_send(message) {
  // 使用 TRIGGER_KEYWORDS 数组中的所有关键词进行过滤
  let filteredMessage = message;
  TRIGGER_KEYWORDS.forEach((keyword) => {
    filteredMessage = filteredMessage.replace(new RegExp(keyword, "g"), "");
  });

  fetch("/human", {
    body: JSON.stringify({
      text: filteredMessage,
      type: "chat",
      interrupt: true,
      sessionid: parseInt(document.getElementById("sessionid").value),
    }),
    headers: {
      "Content-Type": "application/json",
    },
    method: "POST",
    cache: "no-store",
  })
  .then(response => response.json())
  .then(data => {

      console.log('Received chat response:', data);

      if (data.code === 0 && data.data) {

          if(data.type=='text'){
              addChatMessage(data.data, 'left', false, 'szr');
          }else if(data.type=='video'){
              showMediaInTechPlayerHandler(data);
          }else if(data.type=='music'){
              addBackgroundMusic(data);
          }else{
              addChatMessage(data.data, 'left', false, 'szr');
          }

      }else{
          addChatMessage('消息发送失败，请重试', 'left', false, 'szr');
      }
      
      
  })
  .catch(error => {
      console.error('请求发生错误:', error);
      // 可以在聊天窗口中添加错误提示
      addChatMessage('网络错误，或数字人未开启，请检查连接', 'left', false, 'szr');
  });
}

// 添加检查说话状态的函数
function checkSpeakingStatus() {
  fetch("/is_speaking", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      sessionid: parseInt(document.getElementById("sessionid").value),
    }),
    cache: "no-store",
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.code === 0) {
        console.log("当前说话状态:", data.data);

        // 根据说话状态控制麦克风
        if (data.data === true) {
          // 当说话状态为true时，暂停麦克风录音
          if (recordingState === RecordingStateEnum.RECORDING) {
            console.log("说话中，暂停录音");
            isRec = false; // 停止音频数据处理
          }
        } else if (data.data === false) {
          // 当说话状态为false时，恢复麦克风录音
          if (recordingState === RecordingStateEnum.RECORDING && !isRec) {
            console.log("已停止说话，恢复录音");
            isRec = true; // 恢复音频数据处理
          }
        }
      }
    })
    .catch((error) => console.error("检查说话状态出错:", error));
}

// 启动定期检查
function startSpeakingCheck() {
  // 每1秒检查一次
  setInterval(checkSpeakingStatus, 1000);
}

// 访问run接口的函数
function runWithSessionId() {
  const sessionid = parseInt(document.getElementById("sessionid").value);

  return fetch("/run", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      sessionid: sessionid,
    }),
    cache: "no-store",
  })
    .then((response) => response.json())
    .then((data) => {
      console.log("run接口调用结果:", data);
      return data;
    })
    .catch((error) => {
      console.error("调用run接口出错:", error);
      return null;
    });
}
