/**
 * Copyright FunASR (https://github.com/alibaba-damo-academy/FunASR). All Rights
 * Reserved. MIT License  (https://opensource.org/licenses/MIT)
 */
/* 2021-2023 by z<PERSON><PERSON>,mali aihealthx.com */

function WebSocketConnectMethod(config) {
  //定义socket连接方法类

  var speechSokt;
  var connKeeperID;

  var msgHandle = config.msgHandle;
  var stateHandle = config.stateHandle;

  this.wsStart = function () {
    // var Uri = document.getElementById('wssip').value; //"wss://**************:5821/wss/" //设置wss asr online接口地址 如 wss://X.X.X.X:port/wss/
    // var Uri = "wss://www.funasr.com:10096";
    var Uri = "ws://127.0.0.1:10096/";
    // 确保后端WebSocket服务器正确配置了SSL证书。如果是使用自签名证书，需要在客户端浏览器中手动接受该证书。可以通过在浏览器中直接访问https://127.0.0.1:10096来手动接受证书。mkcert创建本地受信任的证书
    // var Uri = "wss://127.0.0.1:10096/";
    if (Uri.match(/wss:\S*|ws:\S*/)) {
      console.log("Uri" + Uri);

      // 添加对本地连接的特殊处理（可选）
      if (Uri.includes("127.0.0.1") || Uri.includes("localhost")) {
        // 添加一个确认对话框，询问用户是否要打开证书验证页面
        if (confirm("需要先激活语音功能，只需激活一次，激活后重启语音即可！")) {
          window.open("https://127.0.0.1:10096", "_blank");
        }
      }
    } else {
      alert("请检查wss地址正确性");
      return 0;
    }

    if ("WebSocket" in window) {
      speechSokt = new WebSocket(Uri); // 定义socket连接对象
      speechSokt.onopen = function (e) {
        onOpen(e);
      }; // 定义响应函数
      speechSokt.onclose = function (e) {
        console.log("onclose ws!");
        //speechSokt.close();
        onClose(e);
      };
      speechSokt.onmessage = function (e) {
        onMessage(e);
      };
      speechSokt.onerror = function (e) {
        onError(e);
      };
      return 1;
    } else {
      alert("当前浏览器不支持 WebSocket");
      return 0;
    }
  };

  // 定义停止与发送函数
  this.wsStop = function () {
    if (speechSokt != undefined) {
      console.log("stop ws!");
      speechSokt.close();
    }
  };

  this.wsSend = function (oneData) {
    if (speechSokt == undefined) return;
    if (speechSokt.readyState === 1) {
      // 0:CONNECTING, 1:OPEN, 2:CLOSING, 3:CLOSED

      speechSokt.send(oneData);
    }
  };

  // SOCEKT连接中的消息与状态响应
  function onOpen(e) {
    // 发送json
    var chunk_size = new Array(5, 10, 5);
    var request = {
      chunk_size: chunk_size,
      wav_name: "h5",
      is_speaking: true,
      chunk_interval: 10,
      itn: getUseITN(),
      mode: getAsrMode(),
    };
    if (isfilemode) {
      request.wav_format = file_ext;
      if (file_ext == "wav") {
        request.wav_format = "PCM";
        request.audio_fs = file_sample_rate;
      }
    }

    var hotwords = getHotwords();

    if (hotwords != null) {
      request.hotwords = hotwords;
    }
    console.log(JSON.stringify(request));
    speechSokt.send(JSON.stringify(request));
    console.log("连接成功");
    stateHandle(0);
  }

  function onClose(e) {
    stateHandle(1);
  }

  function onMessage(e) {
    msgHandle(e);
  }

  function onError(e) {
    // info_div.innerHTML = "连接" + e;
    console.log(e);
    stateHandle(2);
  }
}
