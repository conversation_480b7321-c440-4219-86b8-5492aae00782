var reconnectInterval = 5000;
var maxReconnectAttempts = 10;
var reconnectAttempts = 0;

function connectWebSocket() {
  var host = window.location.hostname;
  ws = new WebSocket("ws://" + host + ":10002");

  ws.onopen = function () {
    console.log("Connected to WebSocket on port 10002");
    reconnectAttempts = 0;

    var loginMessage = JSON.stringify({ Username: "User" });
    ws.send(loginMessage);
    loginMessage = JSON.stringify({ Output: "1" });
    ws.send(loginMessage);
  };

  ws.onmessage = function (e) {
    var messageData = JSON.parse(e.data);

    if (messageData.error) {
      console.error("WebSocket error:", messageData.error);
      return;
    }

    if (messageData.Data && messageData.Data.Key) {
      if (messageData.Data.Key == "audio") {
        var value = messageData.Data.HttpValue;
        fetch("/humanaudio", {
          body: JSON.stringify({
            file_url: value,
            sessionid: document.getElementById("sessionid").value,
          }),
          headers: {
            "Content-Type": "application/json",
          },
          method: "POST",
        });
      }
    }
  };

  ws.onclose = function (e) {
    console.log("WebSocket connection closed");
    attemptReconnect();
  };

  ws.onerror = function (e) {
    console.error("WebSocket error:", e);
    setTimeout(function () {
      if (ws.readyState === WebSocket.CLOSED) {
        connectWebSocket();
      }
    }, reconnectInterval);
  };
}

function attemptReconnect() {
  if (reconnectAttempts < maxReconnectAttempts) {
    reconnectAttempts++;
    console.log(
      "Attempting to reconnect... (Attempt " + reconnectAttempts + ")"
    );
    setTimeout(function () {
      connectWebSocket();
    }, reconnectInterval);
  } else {
    console.error(
      "Maximum reconnection attempts reached. Could not reconnect to WebSocket."
    );
  }
}

// 页面加载完成后自动连接WebSocket
window.addEventListener("load", function () {
  connectWebSocket();
});
