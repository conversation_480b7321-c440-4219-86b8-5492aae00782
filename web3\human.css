body {
    padding: 0;
    margin: 0;
    width: 100vw;
    height: 100vh;
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    background-image: url('./images/bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.option {
    margin-bottom: 8px;
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}



.main-title {
    text-align: center;
    font-size: 22px;
    margin: 0;
    padding: 0;
    height: 20px;
    line-height: 20px;
}

/* .video-box {
    position: fixed;
    width: 550px;
    height: 600px;
    min-width: 300px;
    min-height: 200px;
    max-width: 90vw;
    max-height: 90vh;
    background-color: transparent;
    border: 2px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    cursor: move;
    user-select: none;
    top: 50px;
    left: 50px;
    z-index: 999;
    overflow: hidden;
    transition: box-shadow 0.2s ease, width 0.3s ease, height 0.3s ease;
    display: flex;
    flex-direction: column;
} */

/* .video-container {
    flex: 1;
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.video-container audio {
    display: none;
} */

/* .video-box:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.video-box.dragging {
    opacity: 0.9;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
    transform: scale(1.01);
} */

.chat-history {
    overflow-x: hidden;
    overflow-y: auto;
    position: absolute;
    top: 50%;
    left: 0;
    padding: 0 8px;
    width: 100%;
    height: 50%;
    box-sizing: border-box;
}

.chat-history::-webkit-scrollbar {
    display: none;
    /* Chrome Safari */
    width: 0;
    height: 0;
    display: none;
}

.user-box {
    display: none;
    float: right;
    width: 60%;
    margin-bottom: 10px;
}

.user-name {
    text-align: right;
    font-size: 14px;
    color: #434242;
    margin-bottom: 2px;
}

.user-text {
    background-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 8px;
    border-radius: 16px 0 16px 16px;
    color: #666;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 1px 2px 15px rgba(0, 0, 0, 0.1);
}

.robot-box {
    float: left;
    width: 60%;
    margin-bottom: 10px;
}

.robot-name {
    font-size: 14px;
    color: #434242;
    margin-bottom: 2px;
}

.robot-text {
    background-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 8px;
    border-radius: 0 16px 16px 16px;
    color: #666;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 1px 2px 15px rgba(0, 0, 0, 0.1);
}

.btn-microphone {
    position: absolute;
    bottom: 10px;
    left: 20%;
    width: 85px;
    height: 85px;
    background-image: url('./images/microphone.png');
    background-size: cover;
    background-position: center;
    /*display: none;*/
}

.btn-callphone {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    background-image: url('./images/phone_down.png');
    background-size: cover;
    background-position: center;
    cursor: pointer;
    border-radius: 50%;
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

.btn-callphone:hover {
    transform: scale(1.1);
}

.btn-microphone-text {
    position: absolute;
    bottom: 100px;
    left: 40%;
    background-size: cover;
    background-position: center;
    color: white;
    font-size: 18px;
    align-items: flex-start;
    text-align: center;
    display: none;
    /*transition: transform 0.3s ease-in-out;*/
}

.btn-callphone-text {
    position: absolute;
    bottom: 100px;
    left: 60%;
    background-size: cover;
    background-position: center;
    color: white;
    font-size: 18px;
    align-items: flex-start;
    text-align: center;
    display: none;
}

.input-box {
    height: 70px;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    position: relative;
    background-color: rgba(255, 255, 255, 0.12);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 8px 8px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    flex-shrink: 0;
}

.input-text {
    width: calc(100% - 120px);
    /* 减去左边按钮和发送按钮的宽度和间距 */
    height: 36px;
    margin: 0 10px 0 0;
    padding: 8px 12px;
    box-sizing: border-box;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(224, 224, 224, 0.4);
    border-radius: 18px;
    outline: none;
    transition: border-color 0.3s ease, background-color 0.3s ease;
}

.input-text:focus {
    border-color: #007bff;
    background-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.input-btn {
    height: 36px;
    width: 60px;
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 18px;
    background-color: #007bff;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.input-btn:hover {
    background-color: #0056b3;
}

/* 添加媒体查询以适应不同屏幕尺寸 */
@media screen and (max-width: 480px) {
    .input-box {
        height: 60px;
        padding: 8px;
    }

    .input-text {
        width: calc(100% - 100px);
        /* 为移动端调整，留出左边按钮的空间 */
        font-size: 13px;
    }

    .input-btn {
        width: 50px;
        font-size: 13px;
    }
}

/* .form-group {
    display: flex;
    align-items: center;
} */

.flash_message {
    /*position: fixed;*/
    bottom: 10px;
    left: 0px;
    padding: 10px;
    background: rgba(34, 139, 34, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    pointer-events: none;
    display: none;
}

.mic_indicate_color {
    position: absolute;
    top: 35px;
    left: 45%;
    padding: 10px;
    background: rgba(34, 139, 34, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    pointer-events: none;
}

.mic_indicate_text {
    position: absolute;
    top: 55px;
    left: 30%;
    padding: 10px;
    color: white;
    pointer-events: none;
}








/* 
为提交按钮添加视觉识别状态样式
.input-btn.analyzing {
    background-color: #2196F3;
    color: white;
}
*/