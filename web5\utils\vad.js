// VAD (Voice Activity Detection) 工具类
// 基于 @ricky0123/vad-web 实现

// 状态变量
let micVad = null;
let isInitialized = false; // 表示配置和回调已设置
let isCreated = false;     // 表示MicVAD实例已创建
let isRunning = false;     // 表示VAD正在监听

// 配置参数 (保持不变)
let vadConfig = {
    positiveSpeechThreshold: 0.8,
    negativeSpeechThreshold: 0.8,
    minSpeechFrames: 4,
    preSpeechPadFrames: 1,
    redemptionFrames: 3,
    frameSamples: 1024,
    modelURL: './vendors/vad/silero_vad.onnx', // 添加本地模型路径
    // legacyModelURL: './vendors/vad/silero_vad_legacy.onnx', // 添加legacy模型路径
    additionalAudioConstraints: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
    }
};

// 回调函数 (保持不变)
let onSpeechStart = null;
let onSpeechEnd = null;
let onVADMisfire = null;
let onFrameProcessed = null;
let onError = null;

/**
 * 初始化VAD模块，仅设置配置和回调，不创建实例。
 * @param {Object} options 配置选项
 * @returns {boolean} 初始化是否成功
 */
export function initVAD(options = {}) {
    try {
        if (typeof vad === 'undefined' || typeof vad.MicVAD === 'undefined') {
            throw new Error("未找到vad.MicVAD，请确保已加载@ricky0123/vad-web");
        }
        
        vadConfig = { ...vadConfig, ...options };
        
        onSpeechStart = options.onSpeechStart;
        onSpeechEnd = options.onSpeechEnd;
        onVADMisfire = options.onVADMisfire;
        onFrameProcessed = options.onFrameProcessed;
        onError = options.onError;
        
        isInitialized = true;
        return true;
    } catch (e) {
        console.error("VAD模块设置失败:", e);
        if (onError) onError(e);
        return false;
    }
}

/**
 * 创建并启动VAD。如果已创建，则直接启动。
 * @returns {Promise<boolean>} 是否成功启动
 */
export async function startVAD() {
    if (!isInitialized) {
        console.error("VAD未初始化，请先调用 initVAD()");
        return false;
    }
    if (isRunning) {
        console.log("VAD已在运行中。");
        return true;
    }
    
    try {
        // 如果实例还未创建，则创建它
        if (!isCreated) {
            micVad = await vad.MicVAD.new({
                ...vadConfig, // 使用合并后的配置
                
                // --- 修正回调函数 ---
                onSpeechStart: () => {
                    console.log("语音开始");
                    if (onSpeechStart) onSpeechStart();
                },
                
                // onSpeechEnd 现在是 async 函数，以便 await base64转换
                onSpeechEnd: async (audio) => {
                    console.log("语音结束，音频长度:", audio.length);
                    if (onSpeechEnd) {
                        // 正确处理异步转换
                        const wavBase64 = await convertToWavBase64(audio);
                        onSpeechEnd(audio, wavBase64);
                    }
                },
                
                onVADMisfire: () => {
                    console.log("VAD误触发");
                    if (onVADMisfire) onVADMisfire();
                },
                
                onFrameProcessed: (probabilities) => {
                    if (onFrameProcessed) onFrameProcessed(probabilities);
                }
            });
            isCreated = true;
        }
        
        // 启动监听
        micVad.start();
        isRunning = true;
        console.log("VAD已启动");
        return true;

    } catch (e) {
        console.error("启动VAD失败:", e);
        if (onError) onError(e);
        // 如果启动失败，重置状态
        isCreated = false;
        isRunning = false;
        micVad = null;
        return false;
    }
}

/**
 * 暂停VAD检测。实例仍在，只是不处理音频。
 */
export function pauseVAD() {
    if (!isRunning || !micVad) {
        return;
    }
    micVad.pause();
    isRunning = false;
    console.log("VAD已暂停");
}

/**
 * 恢复VAD检测。
 */
export function resumeVAD() {
    if (isRunning || !isCreated || !micVad) {
        return;
    }
    micVad.start(); // 根据库API，start也用于恢复
    isRunning = true;
    console.log("VAD已恢复");
}

/**
 * 停止并销毁VAD实例，释放所有资源。
 */
export function stopVAD() {
    if (!isCreated || !micVad) {
        return;
    }
    
    // 使用 .destroy() 方法来释放麦克风等资源
    micVad.destroy(); 
    micVad = null;
    isCreated = false;
    isRunning = false;
    console.log("VAD已停止并销毁");
}

/**
 * 检查VAD是否正在运行
 * @returns {boolean} 是否正在运行
 */
export function isVADRunning() {
    return isRunning;
}


/**
 * 将Float32Array转换为base64编码的WAV格式
 * 这是异步函数，返回一个 Promise
 * @param {Float32Array} audioData 音频数据
 * @returns {Promise<string>} base64编码的WAV数据URL
 */
function convertToWavBase64(audioData) {
    const sampleRate = 16000;
    const buffer = new ArrayBuffer(44 + audioData.length * 2);
    const view = new DataView(buffer);

    // 写入WAV头 (这部分代码是正确的，无需修改)
    view.setUint8(0, 0x52); view.setUint8(1, 0x49); view.setUint8(2, 0x46); view.setUint8(3, 0x46); // "RIFF"
    view.setUint32(4, 36 + audioData.length * 2, true); // 文件大小
    view.setUint8(8, 0x57); view.setUint8(9, 0x41); view.setUint8(10, 0x56); view.setUint8(11, 0x45); // "WAVE"
    view.setUint8(12, 0x66); view.setUint8(13, 0x6d); view.setUint8(14, 0x74); view.setUint8(15, 0x20); // "fmt "
    view.setUint32(16, 16, true); // 格式块长度
    view.setUint16(20, 1, true); // 格式类型 (1 = PCM)
    view.setUint16(22, 1, true); // 通道数
    view.setUint32(24, sampleRate, true); // 采样率
    view.setUint32(28, sampleRate * 2, true); // 字节率
    view.setUint16(32, 2, true); // 块对齐
    view.setUint16(34, 16, true); // 每样本位数
    view.setUint8(36, 0x64); view.setUint8(37, 0x61); view.setUint8(38, 0x74); view.setUint8(39, 0x61); // "data"
    view.setUint32(40, audioData.length * 2, true); // 数据大小

    let index = 44;
    for (let i = 0; i < audioData.length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i])) * 0x7FFF;
        view.setInt16(index, sample, true);
        index += 2;
    }

    // --- 修正返回Promise的部分 ---
    return new Promise((resolve) => {
        const blob = new Blob([view], { type: 'audio/wav' });
        const reader = new FileReader();
        reader.onloadend = () => {
            // reader.result 是 "data:audio/wav;base64,..." 格式
            resolve(reader.result); 
        };
        reader.readAsDataURL(blob);
    });
}

// destroyVAD 可以简化为直接调用 stopVAD
export function destroyVAD() {
    stopVAD();
    isInitialized = false; // 额外重置初始化状态
}