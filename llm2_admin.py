#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成管理后台配置的LLM模块
基于原始llm2.py，但会优先使用管理后台的配置
"""

import time
import os
from basereal import BaseReal
from logger import logger
from admin_config import get_llm_config_from_admin

def llm_response(message, nerfreal: BaseReal):
    """
    使用管理后台配置的LLM响应函数
    
    Args:
        message: 用户输入的消息
        nerfreal: BaseReal实例
        
    Returns:
        str: LLM的完整响应
    """
    start = time.perf_counter()
    
    # 尝试从管理后台获取配置
    admin_config = get_llm_config_from_admin()
    
    if admin_config:
        # 使用管理后台配置
        api_key = admin_config['api_key']
        base_url = admin_config['base_url']
        model_name = admin_config['model_name']
        logger.info(f"使用管理后台配置: {admin_config['name']}")
    else:
        # 使用默认配置（原llm2.py中的配置）
        # api_key = 'sk-398dd5bb25da4668a00c0c27944f98d8'
        # base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        # model_name = "qwen-plus"
        
        api_key='0d4ad126d94a4c2ba7a1e93fb8a32841.0Dr1eRMzi5NGVKNL'
        base_url="https://open.bigmodel.cn/api/paas/v4"
        # model_name="glm-4-flash-250414"
        model_name = 'glm-4.5-flash'
        
        logger.warning("管理后台配置不可用，使用默认配置")
    
    from openai import OpenAI
    client = OpenAI(
        api_key=api_key,
        base_url=base_url,
    )
    
    end = time.perf_counter()
    logger.info(f"llm Time init: {end-start}s")
    
    try:
        completion = client.chat.completions.create(
            model=model_name,
            messages=[
                {'role': 'system', 'content': 'You are a helpful assistant.'},
                {'role': 'user', 'content': message}
            ],
            stream=True,
            # 通过以下设置，在流式输出的最后一行展示token使用信息
            stream_options={"include_usage": True}
        )
        
        result = ""
        first = True
        full_response = ""
        
        for chunk in completion:
            if len(chunk.choices) > 0:
                if first:
                    end = time.perf_counter()
                    logger.info(f"llm Time to first chunk: {end-start}s")
                    first = False

                msg = chunk.choices[0].delta.content
                if msg is not None:  # 更严格的None检查
                    full_response += msg
                    lastpos = 0

                    # 按标点符号分割消息
                    for i, char in enumerate(msg):
                        if char in ",.!;:，。！？：；":
                            result = result + msg[lastpos:i+1]
                            lastpos = i + 1
                            if len(result) > 10:
                                logger.info(result)
                                nerfreal.put_msg_txt(result)
                                result = ""
                    result = result + msg[lastpos:]
        
        end = time.perf_counter()
        logger.info(f"llm Time to last chunk: {end-start}s")
        
        if result:  # 发送剩余的文本
            nerfreal.put_msg_txt(result)
        
        return full_response
        
    except Exception as e:
        logger.error(f"LLM调用失败: {e}")
        logger.error(f"错误类型: {type(e).__name__}")
        logger.error(f"使用的配置 - API Key: {api_key[:10]}..., Base URL: {base_url}, Model: {model_name}")

        # 如果是网络连接错误，提供更具体的错误信息
        if "Connection" in str(e) or "timeout" in str(e).lower():
            error_msg = "网络连接失败，请检查网络设置。"
        elif "API key" in str(e) or "Unauthorized" in str(e):
            error_msg = "API密钥无效，请检查配置。"
        elif "model" in str(e).lower():
            error_msg = "模型配置错误，请检查模型名称。"
        else:
            error_msg = "抱歉，我现在无法回答您的问题。"

        nerfreal.put_msg_txt(error_msg)
        return error_msg

def test_llm_config():
    """
    测试LLM配置是否正常工作
    """
    print("测试LLM配置...")
    
    # 检查管理后台配置
    admin_config = get_llm_config_from_admin()
    if admin_config:
        print(f"✓ 找到管理后台配置: {admin_config['name']}")
        print(f"  - Model: {admin_config['model_name']}")
        print(f"  - Base URL: {admin_config['base_url']}")
    else:
        print("✗ 未找到管理后台配置，将使用默认配置")
    
    # 测试API连接
    try:
        from openai import OpenAI
        
        if admin_config:
            client = OpenAI(
                api_key=admin_config['api_key'],
                base_url=admin_config['base_url'],
            )
            model_name = admin_config['model_name']
        else:
            client = OpenAI(
                api_key='sk-398dd5bb25da4668a00c0c27944f98d8',
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )
            model_name = "qwen-plus"
        
        # 发送测试消息
        print(f"正在测试API连接...")
        print(f"  - API Key: {admin_config['api_key'][:10] if admin_config else 'sk-9e1c62683d'}...")
        print(f"  - Base URL: {admin_config['base_url'] if admin_config else 'https://dashscope.aliyuncs.com/compatible-mode/v1'}")
        print(f"  - Model: {model_name}")

        response = client.chat.completions.create(
            model=model_name,
            messages=[{'role': 'user', 'content': '你好'}],
            max_tokens=10
        )

        print("✓ LLM API连接测试成功")
        print(f"  响应: {response.choices[0].message.content}")
        return True

    except Exception as e:
        print(f"✗ LLM API连接测试失败: {e}")
        print(f"  错误类型: {type(e).__name__}")
        if hasattr(e, 'response'):
            print(f"  HTTP状态码: {e.response.status_code if e.response else 'N/A'}")
        return False

if __name__ == "__main__":
    # 运行测试
    test_llm_config()
