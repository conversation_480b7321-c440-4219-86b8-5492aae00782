<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数字人展示</title>
    <link rel="stylesheet" href="human.css" />
</head>

<body class="flex-center">
    <div class="box">

        <div class="video-box">
            <video id="rtc_media_player" style="width:100%;height:100%;" muted autoplay playsinline></video>
            <div class="btn-callphone" id="btnButtonCall"></div>
        </div>

        <div class="input-box flex-center">
            <textarea class="input-text" id="message" onkeydown="if(event.keyCode==13 && !event.shiftKey){event.preventDefault();message_get();return false;}">你是谁？</textarea>
            <button id="btnSubmit" type="button" class="input-btn" onclick="message_get()">提交</button>
            <input type="hidden" id="sessionid" value="0">
        </div>

        

    </div>

    <script src="srs.sdk.js"></script>
    <script type="text/javascript" src="http://cdn.sockjs.org/sockjs-0.3.4.js"></script>
    <script type="text/javascript" src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
    <script type="text/javascript" src="asr/recorder-core.js"></script>
    <script type="text/javascript" src="asr/wav.js"></script>
    <script type="text/javascript" src="asr/pcm.js"></script>
    <script type="text/javascript" src="asr/wsconnecter.js"></script>
    <script type="text/javascript" src="srs_utils.js" charset="utf-8"></script>
    
</body>
</html> 