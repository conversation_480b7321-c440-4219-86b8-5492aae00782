<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实时交互数字人</title>


    <!-- 样式 -->
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #f72585;
            --text-primary: #333;
            --text-secondary: #666;
            --background-light: #ffffff;
            --background-dark: #f8f9fa;
            --border-color: #e0e0e0;
            --success-color: #2ec4b6;
            --warning-color: #ff9f1c;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --border-radius: 12px;
            --transition: all 0.1s ease;
        }

        /* 移除媒体按钮悬停样式 */
        #mediaRemoveButton:hover {
            background: rgba(255, 165, 0, 0.3) !important;
            border-color: rgba(255, 165, 0, 0.8) !important;
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(255, 165, 0, 0.6) !important;
        }

        /* 为移除媒体按钮添加提示气泡 */
        #mediaRemoveButton {
            position: absolute;
        }

        #mediaRemoveButton .tooltip {
            position: absolute;
            bottom: 40px;
            /* 改为顶部显示，从bottom改为top */
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background: rgba(255, 165, 0, 0.9);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            pointer-events: none;
            z-index: 100;
            font-weight: 500;
            letter-spacing: 1px;
        }

        #mediaRemoveButton .tooltip::after {
            content: "";
            position: absolute;
            bottom: -8px;
            /* 改为底部 */
            left: 50%;
            transform: translateX(-50%) rotate(180deg);
            /* 旋转箭头 */
            border-width: 4px;
            border-style: solid;
            border-color: transparent transparent rgba(255, 165, 0, 0.9) transparent;
            top: auto;
            /* 移除top属性 */
        }

        #mediaRemoveButton:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
            overflow: hidden;
            color: var(--text-primary);
        }

        @font-face {
            font-family: 'ChillLongCangKaiMedium';
            src: url('./font-family/ChillLongCangKai_Medium.otf') format('opentype');
            font-weight: normal;
            font-style: normal;
        }

        /* @font-face {
            font-family: 'ChillLongCangKaiExtraBold';
            src: url('./font-family/ChillLongCangKai_ExtraBold.otf') format('opentype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'ChillLongCangKaiBold';
            src: url('./font-family/ChillLongCangKai_Bold.otf') format('opentype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'ChillLongCangKaiRegular';
            src: url('./font-family/ChillLongCangKai_Regular.otf') format('opentype');
            font-weight: normal;
            font-style: normal;
        } */



        /* 媒体框架样式 */
        .media-frame {
            /* 让其可以left相对于其父元素 */
            position: absolute;
            width: 60vw;
            height: calc(9 / 16 * 60vw);
            max-width: 60vw;
            max-height: 80vh;
            min-width: 400px;
            min-height: 225px;
            background: transparent;
            padding: 10px;
            border-radius: 20px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            backdrop-filter: blur(20px);
            overflow: hidden;
            transform-origin: center center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 底部粒子瀑布效果 */
        .media-frame .particle-cascade {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(0, 212, 255, 0.8);
            border-radius: 50%;
            animation: cascade 3s linear infinite;
            box-shadow: 0 0 6px rgba(0, 212, 255, 0.8);
            top: 0;
        }

        @keyframes cascade {
            0% {
                transform: translateY(0);
                opacity: 0;
            }

            5% {
                opacity: 1;
            }

            95% {
                opacity: 1;
            }

            100% {
                transform: translateY(calc(100vh - 4px));
                opacity: 0;
            }
        }

        .media-frame.active {
            transform: scale(1) rotate(0deg);
            opacity: 1;
            visibility: visible;
        }

        /* 位置变化逻辑 */
        .media-frame.position-left {
            left: 6%;
        }

        .media-frame.position-right {
            right: 6%;
        }

        .media-frame.position-center {
            /* 覆盖任何定位 */
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
        }

        .media-frame.position-bottom {
            left: 0;
            right: 0;
            bottom: 40px;
            margin-left: auto;
            margin-right: auto;
            transform: none;
            /* 移除transform */
        }

        .media-frame.position-top {
            left: 0;
            right: 0;
            top: 40px;
            margin-left: auto;
            margin-right: auto;
            transform: none;
            /* 移除transform */
        }

        /* 科技光晕效果 */
        .media-frame::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            background: linear-gradient(45deg,
                    rgba(0, 212, 255, 0.2),
                    rgba(0, 150, 255, 0.1),
                    rgba(0, 212, 255, 0.2),
                    rgba(0, 150, 255, 0.1));
            border-radius: 24px;
            z-index: -1;
            filter: blur(12px);
            opacity: 0.8;
        }

        /* 脉动光环 */
        .media-frame::after {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 30px;
            z-index: -1;
            animation: pulseRing 2s ease-in-out infinite;
        }

        @keyframes pulseRing {

            0%,
            100% {
                transform: scale(1);
                opacity: 0.4;
            }

            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        /* 数据流效果 */
        .data-stream-right {
            position: absolute;
            top: 0;
            right: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(180deg,
                    transparent 0%,
                    rgba(0, 212, 255, 0.8) 20%,
                    rgba(0, 212, 255, 0.4) 80%,
                    transparent 100%);
            animation: dataFlowDown 2s ease-in-out infinite;
        }

        .data-stream-left {
            position: absolute;
            top: 0;
            left: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(0deg,
                    transparent 0%,
                    rgba(0, 212, 255, 0.8) 20%,
                    rgba(0, 212, 255, 0.4) 80%,
                    transparent 100%);
            animation: dataFlowUp 2s ease-in-out infinite;
        }

        .data-stream-top {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg,
                    transparent 0%,
                    rgba(0, 212, 255, 0.8) 20%,
                    rgba(0, 212, 255, 0.4) 80%,
                    transparent 100%);
            animation: dataFlowRight 2s ease-in-out infinite;
        }

        .data-stream-bottom {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(270deg,
                    transparent 0%,
                    rgba(0, 212, 255, 0.8) 20%,
                    rgba(0, 212, 255, 0.4) 80%,
                    transparent 100%);
            animation: dataFlowLeft 2s ease-in-out infinite;
        }

        @keyframes dataFlowDown {

            0%,
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }

            50% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes dataFlowUp {

            0%,
            100% {
                transform: translateY(100%);
                opacity: 0;
            }

            50% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes dataFlowRight {

            0%,
            100% {
                transform: translateX(-100%);
                opacity: 0;
            }

            50% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes dataFlowLeft {

            0%,
            100% {
                transform: translateX(100%);
                opacity: 0;
            }

            50% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 媒体内容样式 */
        .media-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            /* padding: 10px; */
            /* margin: 10px; */
            background: transparent;
        }

        .media-item {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 20px;
            box-shadow: none;
            background: transparent !important;
            -webkit-backdrop-filter: none !important;
            backdrop-filter: none !important;
        }

        .media-item.fill {
            object-fit: cover;
        }

        /* 上传区域样式 */
        .upload-zone {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 3px dashed rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            background: transparent;
        }

        .upload-zone:hover {
            border-color: rgba(0, 212, 255, 0.6);
            background: rgba(0, 212, 255, 0.05);
            box-shadow: inset 0 0 30px rgba(0, 212, 255, 0.1);
        }

        .upload-zone.dragover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            opacity: 0.7;
            color: #00d4ff;
            filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
        }

        .upload-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 18px;
            text-align: center;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            text-align: center;
        }

        /* 关闭按钮样式 */
        .close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 32px;
            height: 32px;
            background: rgba(0, 199, 244, 0.1);
            /* 改为蓝色 */
            border: 2px solid rgba(0, 199, 244, 0.5);
            /* 改为蓝色 */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            opacity: 0;
            /* 默认隐藏 */
        }

        /* 鼠标悬停在media-frame上时显示关闭按钮 */
        .media-frame:hover .close-button {
            opacity: 1;
        }

        .close-button:hover {
            background: rgba(0, 199, 244, 0.3);
            /* 改为蓝色 */
            border-color: rgba(0, 199, 244, 0.8);
            /* 改为蓝色 */
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(0, 199, 244, 0.6);
            /* 改为蓝色 */
        }

        .close-button::before,
        .close-button::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 2px;
            background: #00c7f4;
            /* 改为蓝色 */
            border-radius: 1px;
        }

        /* 禁用移除媒体按钮的伪元素 */
        #mediaRemoveButton::before,
        #mediaRemoveButton::after {
            display: none !important;
        }

        .close-button::before {
            transform: rotate(45deg);
        }

        .close-button::after {
            transform: rotate(-45deg);
        }

        /* 动画类 */
        .media-frame.anim-scale {
            transform: scale(0.6);
        }

        .media-frame.anim-scale.active {
            transform: scale(1);
            opacity: 1;
            visibility: visible;
        }

        /* 旋转动画 */
        .media-frame.anim-rotate {
            transform: rotate(-180deg) scale(0.6);
        }

        .media-frame.anim-rotate.active {
            transform: rotate(0) scale(1);
            opacity: 1;
            visibility: visible;
        }

        /* 滑动动画 */
        .media-frame.anim-slide {
            transform: translateY(-100px);
        }

        .media-frame.anim-slide.active {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        /* 翻转动画 */
        .media-frame.anim-flip {
            transform: perspective(1000px) rotateX(-90deg);
        }

        .media-frame.anim-flip.active {
            transform: perspective(1000px) rotateX(0);
            opacity: 1;
            visibility: visible;
        }

        /* 弹性动画 */
        .media-frame.anim-bounce {
            transform: scale(0.3);
            transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .media-frame.anim-bounce.active {
            transform: scale(1);
            opacity: 1;
            visibility: visible;
        }

        /* 淡入动画 */
        .media-frame.anim-fade {
            transform: scale(1);
        }

        .media-frame.anim-fade.active {
            opacity: 1;
            visibility: visible;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .media-frame {
                width: 90vw;
                height: calc(9 / 16 * 90vw);
                max-width: 90vw;
                max-height: calc(9 / 16 * 90vw);
                min-width: 192px;
                min-height: 108px;
                z-index: 1010;
            }

            .media-frame.position-left,
            .media-frame.position-right,
            .media-frame.position-top {
                left: 0;
                right: 0;
                top: 10px;
                margin-left: auto;
                margin-right: auto;
                transform: none;
                /* 移除transform */
            }

            .media-frame.position-center {
                left: 0;
                right: 0;
                top: 50%;
                /* transform: translateY(-50%); */
                margin-left: auto;
                margin-right: auto;
            }

            .media-frame.position-left.active,
            .media-frame.position-right.active {
                transform: scale(1);
                /* 只保留缩放效果 */
            }
        }

        /* 动画按钮组样式 */
        .animation-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }

        .animation-button {
            padding: 10px 16px;
            background-color: var(--background-dark);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            min-width: 100px;
            position: relative;
            overflow: hidden;
        }

        .animation-button:hover {
            background-color: rgba(67, 97, 238, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(67, 97, 238, 0.2);
        }

        .animation-button:active {
            transform: translateY(0);
        }

        .animation-button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
        }

        .animation-button.active:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4);
        }

        /* 添加图标样式 */
        .animation-button svg {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            stroke: currentColor;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            fill: none;
        }

        /* 添加涟漪效果 */
        .animation-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .animation-button:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }

            20% {
                transform: scale(25, 25);
                opacity: 0.3;
            }

            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }

        /* 响应式适配 */
        @media (max-width: 768px) {
            .animation-buttons {
                gap: 6px;
            }

            .animation-button {
                padding: 8px 12px;
                font-size: 13px;
                min-width: 80px;
            }

            .animation-button svg {
                width: 14px;
                height: 14px;
                margin-right: 4px;
            }
        }

        /* 音频播放器科技化 */
        .audio-player {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            background: linear-gradient(135deg, rgba(0, 30, 60, 0.8), rgba(0, 10, 30, 0.9));
            position: relative;
            border-radius: 20px;
        }

        /* 音频播放器背景科技线条 */
        .audio-player::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(90deg, transparent 49%, rgba(0, 212, 255, 0.1) 50%, transparent 51%),
                linear-gradient(0deg, transparent 49%, rgba(0, 212, 255, 0.1) 50%, transparent 51%);
            background-size: 40px 40px;
            pointer-events: none;
            animation: gridMove 10s linear infinite;
        }

        @keyframes gridMove {
            0% {
                transform: translate(0, 0);
            }

            100% {
                transform: translate(40px, 40px);
            }
        }

        .audio-visualizer {
            width: 100%;
            height: 120px;
            margin-bottom: 30px;
            display: flex;
            align-items: end;
            justify-content: center;
            gap: 3px;
            background: linear-gradient(180deg, transparent 0%, rgba(0, 212, 255, 0.1) 100%);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            will-change: contents;
            /* 提示浏览器内容会频繁变化 */
            transform: translateZ(0);
            /* 启用GPU加速 */
        }

        /* 可视化器光效 */
        /* .audio-visualizer::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(0, 212, 255, 0.3), 
                transparent
            );
            animation: scannerMove 3s ease-in-out infinite;
        } */

        @keyframes scannerMove {
            0% {
                left: -100%;
            }

            100% {
                left: 100%;
            }
        }

        .bar {
            width: 4px;
            background: linear-gradient(180deg, #00d4ff, #0066cc);
            border-radius: 2px;
            transition:
                height 0.1s cubic-bezier(0.12, 0.8, 0.24, 1.0),
                /* 上升：快速启动，缓慢停止 */
                height 0.2s cubic-bezier(0.3, 0, 0.5, 0.2);
            /* 下降：缓慢启动，快速停止 */
            will-change: height;
            /* 修复语法，只保留属性名 */
            transform: translateZ(0);
            /* 启用GPU加速 */
            box-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
        }

        @keyframes pulse {
            0% {
                height: 10px;
            }

            100% {
                height: 60px;
            }
        }

        .audio-controls {
            width: 100%;
            max-width: 500px;
            position: relative;
        }

        .audio-info {
            text-align: center;
            margin-bottom: 25px;
            position: relative;
        }

        .audio-title {
            color: #fff;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .audio-duration {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .media-progress-container {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin-bottom: 20px;
            cursor: pointer;
            position: relative;
            border: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
        }

        .media-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #0099cc);
            border-radius: 4px;
            width: 0%;
            position: absolute;
            left: 0;
            top: 0;
            pointer-events: none;
            transition: none;
            /* 移除过渡效果，确保实时更新 */
        }

        .media-progress-handle {
            position: absolute;
            top: 50%;
            width: 18px;
            height: 18px;
            background: radial-gradient(circle, #00d4ff, #0099cc);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: grab;
            left: 0%;
            box-shadow:
                0 0 20px rgba(0, 212, 255, 0.8),
                inset 0 0 8px rgba(255, 255, 255, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            z-index: 2;
            transition: transform 0.1s ease;
        }

        .media-progress-handle:active {
            cursor: grabbing;
            transform: translate(-50%, -50%) scale(1.1);
        }

        /* 确保进度条容器可点击 */
        .media-progress-container {
            position: relative;
            cursor: pointer;
            z-index: 1;
        }

        .control-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .play-button {
            width: 70px;
            height: 70px;
            background: radial-gradient(circle, #00d4ff, #0099cc);
            border: 3px solid rgba(0, 212, 255, 0.5);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow:
                0 8px 25px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .play-button:hover {
            transform: scale(1.1);
            box-shadow:
                0 12px 35px rgba(0, 212, 255, 0.5),
                inset 0 0 30px rgba(255, 255, 255, 0.2);
        }

        /* 播放按钮光环 */
        .play-button::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 50%;
            animation: buttonPulse 2s ease-in-out infinite;
        }

        @keyframes buttonPulse {

            0%,
            100% {
                transform: scale(1);
                opacity: 0.3;
            }

            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        .play-icon,
        .pause-icon {
            width: 0;
            height: 0;
            /* border-style: solid; */
            z-index: 1;
        }

        .play-icon {
            border-left: 18px solid white;
            border-top: 12px solid transparent;
            border-bottom: 12px solid transparent;
            margin-left: 4px;
        }

        .pause-icon {
            width: 16px;
            height: 24px;
            position: relative;
        }

        .pause-icon::before,
        .pause-icon::after {
            content: '';
            position: absolute;
            width: 5px;
            height: 24px;
            background: white;
            border-radius: 2px;
        }

        .pause-icon::before {
            left: 0;
        }

        .pause-icon::after {
            right: 0;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
        }


        .draggable {
            cursor: grab;
            filter: brightness(1.05);
        }

        .dragging {
            cursor: grabbing;
            filter: brightness(1.05);
        }

        /* ASR状态指示器样式 */
        /* #asr-status {
            position: fixed;
            bottom: 20px;
            right: 85px;
            background-color: rgba(255, 255, 255, 0.85);
            color: var(--success-color);
            padding: 8px 15px;
            border-radius: 50px;
            font-size: 14px;
            box-shadow: var(--box-shadow);
            z-index: 1002;
            backdrop-filter: blur(4px);
            cursor: pointer;
            transition: var(--transition);
        }
        
        #asr-status.paused {
            color: var(--warning-color);
        }
        
        #asr-status:hover {
            background-color: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        } */

        .main-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        /* 性能监控样式 */
        #performance-stats {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            display: none;
            /* 默认隐藏 */
        }

        #performance-stats.visible {
            display: block;
            /* 显示时添加.visible类 */
        }

        #media {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #canvas {
            image-rendering: high-quality;
            /* 确保不会因为缩放而模糊 */
            transform: translateZ(0);
            /* 启用GPU加速 */
            backface-visibility: hidden;
            perspective: 1000px;
            -webkit-font-smoothing: subpixel-antialiased;
            position: absolute;
            z-index: 1000;
            cursor: default;
            transition: var(--transition);
            display: none;
        }

        #canvas.draggable {
            cursor: grab;
            filter: brightness(1.05);
        }

        #canvas.dragging {
            cursor: grabbing;
            filter: brightness(1.05);
        }

        #canvas.show {
            display: block;
        }

        .canvas-status {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 13px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.85);
            padding: 8px 16px;
            border-radius: 50px;
            z-index: 1001;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            backdrop-filter: blur(4px);
        }

        /* 设置按钮样式 */
        #settings-button {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            box-shadow: var(--box-shadow);
            cursor: pointer;
            z-index: 1002;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: var(--transition);
            border: none;
        }

        #settings-button:hover {
            transform: scale(1.05);
            background-color: var(--primary-hover);
        }

        #settings-button img {
            width: 28px;
            height: 28px;
            opacity: 0.8;
            transition: var(--transition);
        }

        #settings-button:hover img {
            opacity: 1;
        }

        /* 弹窗样式 */
        /* #settings-modal, */
        #test-modal {
            display: none;
            position: absolute;
            top: 95px;
            right: 30px;
            width: 400px;
            background-color: var(--background-light);
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 1005;
            padding: 0;
            min-height: 300px;
            max-height: calc(80vh);
            overflow: hidden;
            transition: var(--transition);
        }

        #test-modal.show {
            display: block;
        }

        .modal-scroll-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            min-height: calc(300px - 288px);
            max-height: calc(100vh - 288px);
            -webkit-overflow-scrolling: touch;
            /* 增加iOS滚动支持 */
        }

        @media (max-width: 768px) {

            #settings-modal {
                width: 60vw !important;
            }

            #test-modal {
                left: 0;
                top: auto;
                bottom: 0;
                width: calc(100%);
                height: calc(40vh);
            }

            #settings-button {
                width: 56px;
                height: 56px;
            }

            #settings-button img {
                width: 28px;
                height: 28px;
            }

            .canvas-status {
                display: none;
            }

            .modal-scroll-container {
                flex: 1;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                min-height: calc(35vh - 588px);
                max-height: calc(100vh - 588px);
                padding: 15px;
                margin-bottom: 0;
            }


        }

        .modal-header {
            flex-shrink: 0;
            /* 防止header被压缩 */
            height: 40px;
            /* 明确设置header高度 */
        }


        /* 确保内容不会被底部遮挡 */
        .modal-section:last-child {
            margin-bottom: 15px;
            padding-bottom: 15px;
            /* 增加底部内边距 */
        }

        /* 优化滚动条样式 */
        .modal-scroll-container::-webkit-scrollbar {
            width: 4px;
            /* 移动端使用更细的滚动条 */
        }

        .modal-scroll-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 2px;
        }

        .modal-scroll-container::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
        }

        .modal-scroll-container:hover {
            scrollbar-width: thin;
            /* Firefox */
            scrollbar-color: var(--border-color) transparent;
        }

        .modal-scroll-container::-webkit-scrollbar {
            width: 0;
            /* Chrome, Safari, Opera */
            display: none;
        }

        .modal-scroll-container:hover::-webkit-scrollbar {
            width: 6px;
            display: block;
        }

        .modal-scroll-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .modal-scroll-container::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 10px;
        }

        .modal-header {
            position: sticky;
            top: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 10px 10px 20px;
            border-bottom: 1px solid var(--border-color);
            background-color: rgb(74, 144, 226);
            z-index: 2;
            /* 字体颜色 */
        }

        .modal-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #fff;
        }

        .modal-close-button {
            background: none;
            padding: 10px;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #00000083;
            display: flex;
            align-items: center;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            transition: var(--transition);
            margin: 0px;
        }

        .modal-close-button:hover {
            background-color: rgba(255, 255, 255, 0.712);
            color: #000;
        }

        .modal-section {
            margin-bottom: 22px;
            padding-bottom: 18px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 16px 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }

        .section-subtitle {
            font-size: 14px;
            font-weight: 600;
            margin: 20px 0 16px 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }

        .section-title svg {
            margin-right: 8px;
            color: var(--primary-color);
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-sizing: border-box;
            resize: vertical;
            font-family: inherit;
            transition: var(--transition);
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        .range-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .range-container input[type="range"] {
            flex: 1;
        }

        .range-value {
            min-width: 40px;
            padding: 4px 8px;
            background-color: var(--background-dark);
            border-radius: 4px;
            text-align: center;
            font-size: 13px;
            font-weight: 500;
            border: 0;
        }

        /* 设置项样式 */
        .settings-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        }

        .settings-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-primary, #e0e0e0);
        }

        .settings-control {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .settings-description {
            font-size: 12px;
            color: var(--text-secondary, #a0a0a0);
            line-height: 1.4;
        }



        button {
            padding: 10px 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-right: 8px;
            margin-bottom: 8px;
            transition: var(--transition);
        }

        button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            /* gap: 8px; */
        }

        /* 开关样式 */
        .switch-container {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

        }

        .switch-container label {
            margin: 0 10px 0 0;
            cursor: pointer;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
            margin: 0px 10px 0px 0px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .switch .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .switch .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .switch input:checked+.slider {
            background-color: var(--primary-color);
        }

        .switch input:focus+.slider {
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        .switch-value {
            font-size: 13px;
            color: var(--text-secondary, #a0a0a0);
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        /* 文件上传样式 */
        .file-upload {
            position: relative;
            overflow: hidden;
            margin-top: 8px;
            width: 100%;
        }

        .file-upload-label {
            display: block;
            padding: 12px;
            background-color: var(--background-dark);
            color: var(--text-primary);
            border: 1px dashed var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: var(--transition);
        }

        .file-upload-label:hover {
            background-color: #f0f0f0;
        }

        .file-upload-label svg {
            margin-right: 8px;
            vertical-align: middle;
        }

        .file-upload input[type="file"] {
            position: absolute;
            font-size: 100px;
            opacity: 0;
            right: 0;
            top: 0;
            cursor: pointer;
        }

        /* 按钮变体 */
        .btn-secondary {
            background-color: var(--background-dark);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background-color: #e9ecef;
        }

        .btn-success {
            background-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #25b0a3;
        }

        .btn-warning {
            background-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #f59000;
        }

        /* 颜色选择器样式 */
        input[type="color"] {
            -webkit-appearance: none;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
        }

        input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 8px;
        }

        .color-preview {
            display: flex;
            align-items: center;
        }

        .color-value {
            margin-left: 12px;
            font-family: monospace;
            font-size: 14px;
            padding: 4px 8px;
            background-color: var(--background-dark);
            border-radius: 4px;
        }

        /* ASR iframe */
        .asr-container {
            width: auto;
            border-radius: 12px;
            background-color: #ffffff;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            /* display: subgrid; 移除，因为不适合此场景 */
        }

        #asr-iframe {
            border: none;
            background-color: white;
            height: auto;
            /* Allow height to adjust automatically */
            min-height: 500px;
            /* Set a minimum height if needed */
        }

        /* 响应式样式 */
        @media (max-width: 768px) {

            #settings-button {
                width: 56px;
                height: 56px;
            }

            #settings-button img {
                width: 28px;
                height: 28px;
            }

            .canvas-status {
                display: none;
            }

            .chat-modal {
                left: 0;
                top: auto;
                bottom: 0;
            }

            .chat-modal.show {
                left: 0;
                top: auto;
                bottom: 0;
                display: flex;
            }
        }


        /* 标签页样式 */
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            overflow-x: auto;
            scrollbar-width: none;
            white-space: nowrap;
        }

        .tabs::-webkit-scrollbar {
            display: none;
        }

        .tabs[data-modal="settings"] {
            display: flex;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            /* scrollbar-width: thin; */
            -ms-overflow-style: none;
            /* IE and Edge */
        }

        /* 隐藏滚动条但保留功能 */
        .tabs[data-modal="settings"]::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
        }

        /* 隐藏滚动条但保留功能 */
        /* .tabs[data-modal="settings"]::-webkit-scrollbar-thumb {
            background-color: rgba(0,0,0,0.2);
            border-radius: 4px;
        } */

        .tab-button {
            padding: 10px 16px;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin: 0;
            transition: var(--transition);
        }

        .tab-button:hover {
            color: var(--text-primary);
            background: none;
            transform: none;
        }

        .tab-button.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 隐藏元素 */
        #audio {
            display: none;
        }

        /* 遮罩层 */
        #modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            /*background-color: rgba(0, 0, 0, 0.2);*/
            /*取消遮罩层的模糊效果*/
            /*backdrop-filter: blur(2px);*/
            z-index: 1000;
            transition: var(--transition);
        }

        .status-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.85);
            color: var(--success-color);
            padding: 8px 15px;
            border-radius: 50px;
            font-size: 14px;
            box-shadow: var(--box-shadow);
            z-index: 1002;
            backdrop-filter: blur(4px);
            cursor: pointer;
            transition: var(--transition);
        }

        .status-indicator.paused {
            color: var(--warning-color);
        }

        .status-indicator:hover {
            background-color: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }

        #show-chat-modal {
            position: absolute;
            top: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            box-shadow: var(--box-shadow);
            cursor: pointer;
            z-index: 1003;
            transition: all 0.3s ease;
        }

        #test-chat-media {
            position: fixed;
            top: 150px;
            left: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #42B983;
            color: white;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            align-items: center;
            transition: var(--transition);
            border: none;
            z-index: 1002;
        }

        /* 添加媒体播放测试按钮样式 */
        #test-media-player-btn {
            position: fixed;
            top: 210px;
            left: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #3498db;
            color: white;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
            z-index: 1002;
        }

        #test-media-player-btn:hover {
            transform: scale(1.1);
            background-color: #2980b9;
        }

        #test-media-player-btn svg {
            width: 20px;
            height: 20px;
        }

        #show-chat-modal:hover {
            transform: scale(1.05);
            background-color: var(--primary-hover);
        }

        /* .chat-item {
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            overflow: visible;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        .chat-item .div {
            overflow: visible;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        .chat-item:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
            transform: translateY(-2px);
        } */

        #test-chat-media:hover {
            transform: scale(1.1);
            background-color: #35A171;
        }

        #test-chat-media svg,
        #show-chat-modal svg {
            width: 20px;
            height: 20px;
        }

        #test-human-media:hover {
            transform: scale(1.1);
            background-color: #35A171;
        }

        #test-human-media svg {
            width: 20px;
            height: 20px;
        }

        /* 添加语音识别圆形按钮样式 */
        #voice-recognition-button {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #4361ee;
            color: white;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 1004;
            outline: none;
            display: none;

            /* 禁用长按选择文本 */
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;

            /* 禁用触摸高亮 */
            -webkit-tap-highlight-color: transparent;

            /* 禁止拖动 */
            -webkit-user-drag: none;

            /* 禁用缩放 */
            touch-action: manipulation;
        }


        #voice-recognition-button:active,
        #voice-recognition-button.recording {
            background-color: #f72585;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transform: translateX(-50%) scale(0.95);
        }

        #voice-recognition-button svg {
            width: 30px;
            height: 30px;
        }

        /* 添加录音动画效果 */
        #voice-recognition-button.recording::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: rgba(247, 37, 133, 0.3);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }

            70% {
                transform: scale(1.2);
                opacity: 0;
            }

            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        /* 移动设备适配 */
        @media (max-width: 768px) {
            #voice-recognition-button {
                width: 80px;
                height: 80px;
                bottom: 75px;
                display: flex;
            }

            #voice-recognition-button svg {
                width: 40px;
                height: 40px;
            }

            /* 在语音识别标签页时调整按钮位置 */
            .tab-recognition-active #voice-recognition-button {
                bottom: 20px;
            }
        }

        /* 平板设备适配 */
        @media (min-width: 769px) and (max-width: 1024px) {
            #voice-recognition-button {
                width: 70px;
                height: 70px;
                bottom: 35px;
            }

            #voice-recognition-button svg {
                width: 35px;
                height: 120px;
            }
        }

        /* 桌面设备适配 */
        @media (min-width: 1025px) {
            #voice-recognition-button {
                width: 60px;
                height: 60px;
                bottom: 30px;
                right: 30px;
                left: auto;
                transform: none;
            }

            #voice-recognition-button:active,
            #voice-recognition-button.recording {
                transform: scale(0.95);
            }
        }

        #info_div {
            margin-bottom: 16px;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
            padding: 8px;
            border-radius: 8px;
            background-color: rgba(67, 97, 238, 0.1);
        }

        /* 自定义播放列表样式 */
        .custom-playlist-wrapper {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            max-height: 200px;
            position: relative;
            transition: box-shadow 0.2s ease;
        }

        .custom-playlist-wrapper:focus-within {
            box-shadow: 0 0 0 2px rgba(58, 86, 212, 0.3);
        }

        .custom-playlist-scroll {
            overflow-y: auto;
            max-height: 200px;
            padding: 0;
            -webkit-overflow-scrolling: touch;
            /* 提升iOS滚动体验 */
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) transparent;
        }

        /* 自定义滚动条样式 */
        .custom-playlist-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-playlist-scroll::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .custom-playlist-scroll::-webkit-scrollbar-track {
            background-color: transparent;
        }

        .custom-playlist {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .playlist-item {
            padding: 10px 12px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.2s ease;
            user-select: none;
            /* 防止文本被选中 */
            -webkit-tap-highlight-color: transparent;
            /* 移除iOS点击高亮 */
            position: relative;
        }

        .playlist-item:last-child {
            border-bottom: none;
        }

        .playlist-item:active {
            background-color: rgba(58, 86, 212, 0.3);
        }

        .playlist-item.selected {
            background-color: rgba(58, 86, 212, 0.2);
        }

        .playlist-item.playing {
            font-weight: bold;
            color: var(--primary-color);
            background-color: rgba(58, 86, 212, 0.1);
        }

        .playlist-item.playing::before {
            content: '▶';
            margin-right: 8px;
            font-size: 10px;
        }

        .track-name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 触摸设备样式优化 */
        @media (pointer: coarse) {
            .playlist-item {
                padding: 12px 15px;
                /* 更大的点击区域 */
                min-height: 24px;
                /* 确保有足够的高度 */
            }

            .playlist-controls .btn-small {
                padding: 10px 15px;
                /* 更大的按钮 */
            }
        }

        /* 播放列表控制按钮样式 */
        .playlist-control-btn {
            white-space: nowrap;
            /* 防止文本换行 */
            overflow: hidden;
            /* 隐藏溢出内容 */
            text-overflow: ellipsis;
            /* 文本溢出时显示省略号 */
            min-width: 0;
            /* 允许按钮缩小至比内容小 */
            padding: 8px 10px;
            /* 统一内边距 */
        }

        /* 输入框样式 */
        .input-field {
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            background-color: var(--bg-color);
            color: var(--text-primary);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .input-field:focus {
            border-color: var(--primary-color);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(58, 86, 212, 0.2);
            outline: none;
        }

        .input-field[readonly] {
            background-color: rgba(0, 0, 0, 0.03);
            cursor: default;
        }

        /* 按钮文本切换 */
        .btn-text-short {
            display: none;
            /* 默认隐藏短文本 */
        }

        /* 窄屏幕适配 */
        @media (max-width: 480px) {
            .playlist-controls {
                gap: 5px;
                /* 减小按钮间距 */
            }

            .playlist-control-btn {
                padding: 8px 5px;
                /* 减小内边距 */
            }
        }

        /* 超窄屏幕适配 */
        @media (max-width: 360px) {
            .btn-text-normal {
                display: none;
                /* 隐藏正常文本 */
            }

            .btn-text-short {
                display: inline;
                /* 显示短文本 */
            }

            .playlist-control-btn {
                padding: 8px 3px;
                /* 进一步减小内边距 */
            }
        }

        /* 待恢复的播放列表项目 */
        .playlist-item.pending-restore {
            background-color: rgba(0, 0, 0, 0.05);
            border-left: 3px solid #e18709;
            padding-left: 9px;
            /* 12px - 3px(边框) */
        }

        .playlist-item.pending-restore .track-name {
            color: var(--text-secondary);
        }

        .pending-icon {
            margin-left: 8px;
            opacity: 0.7;
            color: #e18709;
        }

        /* 窄屏幕适配 */
        @media (max-width: 480px) {
            .playlist-controls {
                gap: 5px;
                /* 减小按钮间距 */
            }

            .playlist-control-btn {
                padding: 8px 5px;
                /* 减小内边距 */
            }
        }

        /* 响应式按钮文本样式 */
        @media screen and (max-width: 576px) {
            #browse-folder-btn .full-text {
                display: none;
            }

            #browse-folder-btn .short-text {
                display: inline !important;
            }
        }

        /* 科技感媒体播放器样式 */
        .tech-media-player {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1005;
            background-color: transparent;
            backdrop-filter: none;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.5s ease;
        }

        .tech-media-player.active {
            opacity: 1;
            pointer-events: auto;
        }

        .tech-media-player-inner {
            position: relative;
            width: 60%;
            min-width: 300px;
            max-width: 1200px;
            transform: scale(0.5);
            transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 0 30px rgba(0, 180, 250, 0.5);
        }

        .tech-media-player.active .tech-media-player-inner {
            transform: scale(1);
        }

        .tech-media-content {
            width: 100%;
            height: 100%;
            background-color: rgba(20, 30, 48, 0.8);
            border: 2px solid rgba(0, 180, 250, 0.7);
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .tech-media-content img,
        .tech-media-content video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .tech-player-border {
            position: absolute;
            width: 20px;
            height: 20px;
            border-color: rgb(0, 195, 255);
            border-style: solid;
            border-width: 0;
        }

        .tech-player-border.top-left {
            top: -3px;
            left: -3px;
            border-top-width: 3px;
            border-left-width: 3px;
        }

        .tech-player-border.top-right {
            top: -3px;
            right: -3px;
            border-top-width: 3px;
            border-right-width: 3px;
        }

        .tech-player-border.bottom-left {
            bottom: -3px;
            left: -3px;
            border-bottom-width: 3px;
            border-left-width: 3px;
        }

        .tech-player-border.bottom-right {
            bottom: -3px;
            right: -3px;
            border-bottom-width: 3px;
            border-right-width: 3px;
        }

        .tech-player-close {
            position: absolute;
            top: 10px;
            right: 0px;
            width: 20px;
            height: 20px;
            background-color: transparent;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 10;
            padding: 0;
        }

        .tech-player-close:hover {
            transform: scale(1.2);
            color: rgb(255, 70, 70);
        }

        .tech-player-close svg {
            width: 24px;
            height: 24px;
            /* stroke: currentColor; */
            stroke-width: 2;
        }

        /* 用于数字人位置移动的动画 */
        @keyframes moveDigitalHuman {
            0% {
                transform: translate(0, 0) scale(1);
            }

            100% {
                transform: translate(var(--move-x), var(--move-y)) scale(var(--scale));
            }
        }

        .dh-animating {
            animation: moveDigitalHuman 0.5s forwards ease-in-out;
        }

        /* 对科技感媒体播放器关闭按钮的增强样式 */
        .tech-player-close {
            outline: none !important;
            -webkit-tap-highlight-color: transparent;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .tech-player-close:hover,
        .tech-player-close:focus,
        .tech-player-close:active {
            outline: none !important;
            background-color: transparent !important;
            box-shadow: none !important;
        }

        /* 移除button元素的默认外观 */
        /* button {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        } */

        /* CSS 样式 */
        #background-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            overflow: hidden;
        }

        #background-video,
        #background-gif,
        #background-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.5s ease;
            opacity: 0;
        }

        #background-video.active,
        #background-gif.active,
        #background-image.active {
            opacity: 1;
        }

        .ui-hidden {
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .chat-modal {
            position: fixed;
            right: 110px;
            bottom: 30px;
            width: 395px;
            height: 300px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            display: none;
            flex-direction: column;
            overflow: hidden;
            font-size: 14px;
            transition: all 0.3s ease;
            z-index: 1003;
        }

        .chat-modal-header {
            background: rgba(74, 144, 226, 0.9);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            text-align: center;
            user-select: none;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-modal-scroll-container {
            flex: 1;
            overflow-y: auto;
            overflow-y: hidden;
            /* 将原本的auto修改为hidden，初始隐藏滚动条 */
            padding: 20px 15px;
            padding-bottom: 0;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chat-modal-scroll-container:hover {
            overflow-y: auto;
            /* 当鼠标悬停时，设置为auto，显示滚动条 */
        }

        .chat-modal-scroll-container::-webkit-scrollbar {
            width: 6px;
        }

        .chat-modal-scroll-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        .chat-modal-scroll-container::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 3px;
        }

        .chat-item {
            display: flex;
            gap: 8px;
            align-items: flex-start;
        }

        .chat-item.user {
            flex-direction: row-reverse;
            align-self: flex-end;
        }

        .chat-item img.avatar {
            width: 34px;
            height: 34px;
            border-radius: 50%;
            object-fit: cover;
            flex-shrink: 0;
        }

        .chat-message {
            margin-top: 16px;
            padding: 8px 12px;
            border-radius: 18px;
            max-width: 100%;
            word-wrap: break-word;
            position: relative;
            line-height: 1.4;
            transition: all 0.3s ease;
        }

        .chat-item:not(.user) .chat-message {
            background: rgba(240, 240, 240, 0.9);
            color: #333;
            border-top-left-radius: 4px;
        }

        .chat-item.user .chat-message {
            background: rgba(74, 144, 226, 0.9);
            color: white;
            border-top-right-radius: 4px;
        }

        .chat-message img,
        .chat-message video {
            max-width: 100%;
            border-radius: 8px;
            margin: 8px 0;
            display: block;
        }

        .chat-audio-player {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 25px;
            padding: 8px 16px;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-audio-player audio {
            flex: 1;
            height: 30px;
        }

        @media screen and (max-width: 768px) {
            .chat-modal {
                width: 100vw !important;
                height: 40vh !important;
                left: 0 !important;
                bottom: 0 !important;
            }
        }

        .rich-text {
            margin: 4px 0;
        }

        .rich-text h3 {
            color: inherit;
            margin-top: 5px;
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .rich-text p {
            margin-bottom: 4px;
        }

        .rich-text strong {
            font-weight: 600;
        }

        .rich-text em {
            font-style: italic;
        }

        /* 控制面板样式 */
        .control-panel {
            position: fixed;
            right: 20px;
            top: 20px;
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-height: 80vh;
            overflow-y: auto;
        }

        .control-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .control-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .control-section h3 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 12px;
        }

        .control-group label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .control-group input,
        .control-group select {
            padding: 6px 10px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.8);
        }

        .color-picker-group {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 5px;
            margin-top: 5px;
        }

        .color-btn {
            width: 30px;
            height: 30px;
            border: 2px solid transparent;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .color-btn:hover {
            transform: scale(1.1);
            border-color: rgba(0, 0, 0, 0.3);
        }

        .slider-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .slider-group input[type="range"] {
            flex: 1;
        }

        .slider-group span {
            font-size: 11px;
            color: #666;
            min-width: 30px;
        }

        .btn-group {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 4px 8px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: rgba(74, 144, 226, 0.1);
        }

        .btn.active {
            background: rgba(74, 144, 226, 0.2);
            border-color: rgba(74, 144, 226, 0.5);
        }

        .chat-modal-header.hidden {
            display: none;
        }

        .chat-input-area button:hover {
            background: rgba(74, 144, 226, 1);
        }

        .chat-input-area input:focus,
        .chat-input-area select:focus {
            outline: none;
            border-color: rgba(74, 144, 226, 0.5);
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .chat-input-area {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 12px;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            gap: 10px;
            align-items: flex-end;
            /* 使按钮与文本框底部对齐 */
        }

        .chat-input-area textarea {
            flex: 1;
            min-height: 24px;
            max-height: 72px;
            /* 3行文本的大致高度 */
            padding: 8px 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            resize: none;
            line-height: 1.5;
            /* font-size: 14px; */
            font-family: inherit;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.2s ease;
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;
            /* IE and Edge */
        }

        /* Webkit (Chrome, Safari, etc) */
        .chat-input-area textarea::-webkit-scrollbar {
            display: none;
            width: 0;
        }

        .chat-input-area textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }

        .chat-input-area button {
            padding: 8px 16px;
            height: 40px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            /* font-size: 14px; */
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .chat-input-area button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        .chat-input-area button:active {
            transform: translateY(0);
        }

        .chat-input-area button svg {
            width: 16px;
            height: 16px;
            margin-right: 6px;
        }

        /* 确保消息内容区域不会被输入框遮挡 */
        .chat-modal-scroll-container {
            padding-bottom: 80px;
        }

        /* 添加切换按钮样式 */
        .chat-toggle-btn {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(74, 144, 226, 0.9);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .chat-toggle-btn:hover {
            background: rgba(74, 144, 226, 1);
            transform: translateX(-50%) scale(1.05);
        }

        /* 添加显示状态类 */
        .chat-modal.show {
            display: flex;
        }

        .font-select {
            padding: 6px 10px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.8);
        }


        .font-select:focus {

            outline: none;

            border-color: rgba(74, 144, 226, 0.5);

            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);

        }

        .font-select option {
            font-size: 12px;
        }

        .font-select option:hover {
            background: rgba(74, 144, 226, 0.1);
        }

        .font-select option:checked {
            background: rgba(74, 144, 226, 0.2);
        }

        .font-select option:active {
            background: rgba(74, 144, 226, 0.3);
        }

        .font-select option:focus {
            background: rgba(74, 144, 226, 0.2);
        }

        .font-select option:disabled {
            background: rgba(0, 0, 0, 0.1);
            color: rgba(0, 0, 0, 0.5);
        }


        #reset-dh-button:hover {
            transform: scale(1.05) rotate(15deg);
            background-color: #1aaa55;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        }




        /* ASR 语音识别样式 */
        .asr-radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .asr-radio-option {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .asr-radio-option input[type="radio"] {
            appearance: none;
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            margin-right: 8px;
            display: grid;
            place-content: center;
            transition: var(--transition);
        }

        .asr-radio-option input[type="radio"]::before {
            content: "";
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--primary-color);
            transform: scale(0);
            transition: var(--transition);
        }

        .asr-radio-option input[type="radio"]:checked {
            border-color: var(--primary-color);
        }

        .asr-radio-option input[type="radio"]:checked::before {
            transform: scale(1);
        }

        .asr-radio-option label {
            font-size: 14px;
            color: var(--text-primary);
            cursor: pointer;
        }

        /* ASR文件上传样式 */
        .asr-file-upload {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .asr-file-upload-label {
            display: block;
            padding: 10px 16px;
            background-color: var(--background-dark);
            color: var(--text-primary);
            border: 1px dashed var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: var(--transition);
        }

        .asr-file-upload-label:hover {
            background-color: #f0f0f0;
        }

        .asr-file-upload input[type="file"] {
            font-size: 100px;
            opacity: 0;
            position: absolute;
            right: 0;
            top: 0;
            cursor: pointer;
        }

        /* ASR热词输入框 */
        #asr-var-hot {
            width: 100%;
            height: 80px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
            transition: var(--transition);
        }

        #asr-var-hot:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* ASR按钮样式 */
        .asr-button {
            padding: 10px 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            min-width: 80px;
        }

        .asr-button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        .asr-button:active {
            transform: translateY(0);
        }

        .asr-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        #btnStart {
            background-color: var(--success-color);
        }

        #btnStart:hover {
            background-color: #25b0a3;
        }

        #btnStop {
            background-color: var(--warning-color);
        }

        #btnStop:hover {
            background-color: #f59000;
        }

        /* ASR分割线 */
        .asr-divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 16px 0;
        }

        .asr-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary);
        }

        /* 结果显示区域样式 */
        #asrVarArea {
            width: 100%;
            /* 减去padding的宽度 */
            height: auto;
            padding: 12px;
            margin-bottom: 16px;
            border: 1px solid #ccc;
            border-radius: 8px;
            /* 高度自适应 */
            resize: none;
            font-family: inherit;
            font-size: 14px;
            background-color: var(--background-light);
            box-sizing: border-box;
            /* 重要：包含边框和内边距 */
            transition: border-color 0.2s;
        }

        /* 设置模态框新样式 */
        #settings-modal {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            max-height: 100vh;
            width: 450px;
            margin: 0;
            border-radius: 0;
            border-top-right-radius: var(--border-radius);
            border-bottom-right-radius: var(--border-radius);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
            display: none;
            position: absolute;
            background-color: var(--background-light);
            z-index: 1005;
            padding: 0;
            min-height: 300px;
            overflow: hidden;
        }

        #settings-modal.expanded {
            transform: translateX(0);
        }

        /* 设置模态框展开按钮 */
        #settings-modal-toggle {
            position: fixed;
            top: 50%;
            left: 0;
            width: 30px;
            height: 60px;
            background-color: var(--primary-color);
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 1004;
            transform: translateY(-50%);
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        #settings-modal-toggle:hover {
            background-color: var(--primary-hover);
        }

        #settings-modal-toggle svg {
            width: 16px;
            height: 16px;
            color: white;
            transition: transform 0.3s ease;
        }

        #settings-modal-toggle.expanded svg {
            transform: rotate(180deg);
        }

        /* 气泡提示样式 */
        .tooltip-container {
            position: relative;
        }

        .tooltip {
            position: absolute;
            background-color: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1010;
            pointer-events: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .tooltip-right {
            left: calc(100% + 10px);
            top: 50%;
            transform: translateY(-50%);
        }

        .tooltip-right::before {
            content: "";
            position: absolute;
            top: 50%;
            left: -6px;
            transform: translateY(-50%);
            border-width: 6px 6px 6px 0;
            border-style: solid;
            border-color: transparent var(--primary-color) transparent transparent;
        }

        .tooltip-top {
            bottom: calc(100% + 10px);
            left: 50%;
            transform: translateX(-50%);
        }

        .tooltip-top::before {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-width: 6px 6px 0 6px;
            border-style: solid;
            border-color: var(--success-color) transparent transparent transparent;
        }

        #reset-dh-button .tooltip {
            background-color: var(--success-color);
        }

        #settings-modal-toggle:hover .tooltip,
        #reset-dh-button:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        @media (max-width: 768px) {

            #settings-modal {
                width: 60vw !important;
                min-width: 350px !important;
            }
        }

        .hide {
            display: none !important;
        }

        .file-upload-container {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 30px 20px;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            background-color: rgba(240, 240, 240, 0.2);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }

        @media (max-width: 768px) {
            #mediaTechCenterPositionMode {
                display: none;
            }
        }

        /* 在合适的位置添加VAD控制UI，例如在聊天模态框中 */
        .vad-control-panel {
            padding: 15px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin: 10px 0;
            z-index: 1006;
        }

        .waveform-container {
            /* height: 30px; */
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1004;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #666;
            margin-right: 10px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .status-indicator.active {
            background-color: #007AFF;
            box-shadow: 0 0 10px rgba(0, 122, 255, 0.7);
            transform: scale(1.2);
        }

        .status-text {
            font-size: 14px;
            margin: 10px 0;
            color: #666;
        }

        .settings-container {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }

        .setting-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }

        .setting-item label {
            flex: 0 0 80px;
        }

        .setting-item input[type="range"] {
            flex: 1;
            margin: 0 10px;
        }

        .vision-container {
            background-color: rgba(248, 249, 250, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            /*overflow: hidden;*/
            height: auto;
            /* Change from 100% to auto */
            padding: 24px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .vision-container:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.1);
        }

        /* 视觉感知摄像头样式 */
        .vision-camera {
            width: 100%;
            max-width: 320px;
            height: auto;
            border-radius: 12px;
            border: 2px solid var(--border-color);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .vision-camera:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        /* 摄像头控制按钮容器 */
        .vision-controls {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* 摄像头控制按钮 */
        .vision-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 100px;
            justify-content: center;
        }

        .vision-btn-start {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .vision-btn-start:hover {
            background: linear-gradient(135deg, #45a049, #3d8b40);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
            transform: translateY(-1px);
        }

        .vision-btn-stop {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
        }

        .vision-btn-stop:hover {
            background: linear-gradient(135deg, #d32f2f, #c62828);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
            transform: translateY(-1px);
        }

        .vision-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .vision-container {
                padding: 15px;
            }

            .vision-controls {
                flex-direction: column;
                align-items: center;
            }

            .vision-btn {
                width: 100%;
                max-width: 200px;
            }
        }

        /* ASR iframe */
        .asr-container {
            background-color: rgba(248, 249, 250, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            /*overflow: hidden;*/
            height: auto;
            /* Change from 100% to auto */
        }

        #asr-iframe {
            border: none;
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            height: auto;
            /* Allow height to adjust automatically */
            min-height: 500px;
            /* Set a minimum height if needed */
        }

        /* 响应式样式 */
        @media (max-width: 768px) {
            #settings-modal {
                width: calc(100% - 40px);
                right: 20px;
                /*max-height: 80vh;*/
            }

            #settings-button {
                width: 48px;
                height: 48px;
            }

            #settings-button img {
                width: 24px;
                height: 24px;
            }

            .canvas-status {
                display: none;
            }
        }

        .btn-microphone {
            position: absolute;
            bottom: 10px;
            left: 20%;
            width: 85px;
            height: 85px;
            background-image: url('./images/microphone.png');
            background-size: cover;
            background-position: center;
            /*display: none;*/
        }

        .btn-callphone {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            background-image: url('./images/phone_down.png');
            background-size: cover;
            background-position: center;
            cursor: pointer;
            border-radius: 50%;
            transition: transform 0.2s ease;
            flex-shrink: 0;
        }

        .btn-callphone:hover {
            transform: scale(1.1);
        }

        .btn-microphone-text {
            position: absolute;
            bottom: 100px;
            left: 40%;
            background-size: cover;
            background-position: center;
            color: white;
            font-size: 18px;
            align-items: flex-start;
            text-align: center;
            display: none;
            /*transition: transform 0.3s ease-in-out;*/
        }

        .btn-callphone-text {
            position: absolute;
            bottom: 100px;
            left: 60%;
            background-size: cover;
            background-position: center;
            color: white;
            font-size: 18px;
            align-items: flex-start;
            text-align: center;
            display: none;
        }

        .input-box {
            height: 70px;
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            position: relative;
            background-color: rgba(255, 255, 255, 0.12);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 0 0 8px 8px;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            flex-shrink: 0;
        }

        .input-text {
            width: calc(100% - 120px);
            /* 减去左边按钮和发送按钮的宽度和间距 */
            height: 36px;
            margin: 0 10px 0 0;
            padding: 8px 12px;
            box-sizing: border-box;
            font-size: 14px;
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(224, 224, 224, 0.4);
            border-radius: 18px;
            outline: none;
            transition: border-color 0.3s ease, background-color 0.3s ease;
        }

        .input-text:focus {
            border-color: #007bff;
            background-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }

        .input-btn {
            height: 36px;
            width: 60px;
            padding: 0;
            margin: 0;
            border: none;
            border-radius: 18px;
            background-color: #007bff;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .input-btn:hover {
            background-color: #0056b3;
        }

        /* 添加媒体查询以适应不同屏幕尺寸 */
        @media screen and (max-width: 480px) {
            .input-box {
                height: 60px;
                padding: 8px;
            }

            .input-text {
                width: calc(100% - 100px);
                /* 为移动端调整，留出左边按钮的空间 */
                font-size: 13px;
            }

            .input-btn {
                width: 50px;
                font-size: 13px;
            }
        }

        /* .form-group {
            display: flex;
            align-items: center;
        } */

        .flash_message {
            /*position: fixed;*/
            bottom: 10px;
            left: 0px;
            padding: 10px;
            background: rgba(34, 139, 34, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            pointer-events: none;
            display: none;
        }

        .mic_indicate_color {
            position: absolute;
            top: 35px;
            left: 45%;
            padding: 10px;
            background: rgba(34, 139, 34, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            pointer-events: none;
        }

        .mic_indicate_text {
            position: absolute;
            top: 55px;
            left: 30%;
            padding: 10px;
            color: white;
            pointer-events: none;
        }

        .flex-center {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>

<body>
    <div class="main-container">
        <div id="background-container">
            <div id="background-video"></div>
            <div id="background-gif"></div>
            <div id="background-image"></div>
        </div>

        <div id="media">
            <audio id="audio" autoplay="autoplay"></audio>
            <canvas id="canvas"></canvas>

            <!-- 人脸检测摄像头容器 -->
            <div id="face-detection-container"
                style="display:none; position: fixed; top: 20px; right: 20px; z-index: 1010; background: rgba(0,0,0,0.6); border-radius: 8px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.3); width: 320px; height: 240px;">
                <div style="position: relative; width: 100%; height: 100%;">
                    <video id="face-video" width="320" height="240" autoplay muted
                        style="transform: scaleX(-1); width: 100%; height: 100%; object-fit: cover;"></video>
                    <canvas id="face-canvas" width="320" height="240"
                        style="position: absolute; top: 0; left: 0; transform: scaleX(-1); width: 100%; height: 100%;"></canvas>
                    <div id="face-status"
                        style="position: absolute; bottom: 10px; left: 10px; color: white; font-size: 14px; background: rgba(0,0,0,0.5); padding: 5px 10px; border-radius: 4px;">
                        等待检测...</div>
                    <div id="face-controls"
                        style="position: absolute; top: 10px; right: 10px; display: flex; gap: 5px;">
                        <button id="face-register-btn"
                            style="background: #4CAF50; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; cursor: pointer; display: none;">注册</button>
                        <button id="face-close-btn"
                            style="background: #f44336; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; cursor: pointer;">关闭</button>
                    </div>
                </div>
            </div>

            <!-- 科技感媒体播放框 -->
            <div class="media-frame anim-scale" id="mediaFrame">
                <div id="mediaFrameCloseButton" class="close-button"></div>

                <!-- 新增：移除媒体按钮，放在右下角 并禁用掉close-button的::before和::after-->
                <div id="mediaRemoveButton" class="close-button"
                    style="top: auto; bottom: 15px; background: rgba(255, 165, 0, 0.1); border-color: rgba(255, 165, 0, 0.5);">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="#FFA500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                    <span class="tooltip">清空</span>
                </div>

                <!-- 科技效果元素 -->
                <div class="particle-cascade"></div>
                <div class="data-stream-left"></div>
                <div class="data-stream-right"></div>
                <div class="data-stream-top"></div>
                <div class="data-stream-bottom"></div>

                <div class="media-content" id="mediaContent">
                    <div class="upload-zone">
                        <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                            <polyline points="17,8 12,3 7,8" />
                            <line x1="12" y1="3" x2="12" y2="15" />
                        </svg>
                        <div class="upload-text">拖拽文件到此处或点击上传</div>
                        <div class="upload-hint">支持图片、视频和音频文件</div>
                    </div>
                </div>
            </div>

            <input type="file" id="fileInput" accept="image/*,video/*,audio/*" style="display: none;">

            <!-- 图片预览模态框 -->
            <div id="image-preview-modal" style="display:none; position:fixed; top:0; left:0;
            width:100vw; height:100vh; background:rgba(0,0,0,0.8); 
            align-items:center; justify-content:center; z-index:9999;">
                <img id="image-preview" style="max-width:90%; max-height:90%; border-radius: 10px;" />
            </div>

            <div id="canvas-status" data-ui="ui" class="canvas-status">按住「空格键」可调整数字人，按「Alt」键可调整背景</div>
            <div id="performance-stats" data-ui="ui">FPS: 0 | 帧处理时间: 0ms</div>
        </div>

        <!-- 小屏的语音识别按钮 -->
        <div id="vad-waveform-container" class="waveform-container"
            style="position: fixed; bottom: 40px; left: 50%; transform: translateX(-50%);"></div>
        <button id="voice-recognition-button" data-ui="ui" type="button" class="voice-recognition-button">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
        </button>

        <!-- 设置按钮 -->
        <button id="settings-button" data-ui="ui">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="3"></circle>
                <path
                    d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z">
                </path>
            </svg>
        </button>

        <!-- 设置模态框展开按钮 -->
        <div id="settings-modal-toggle" data-ui="ui">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
            <div class="tooltip tooltip-right">打开控制面板</div>
        </div>

        <!-- 测试模态框 -->
        <div id="test-modal" style="z-index: 1006; right: 40px; top: 230px;">
            <div class="modal-header" id="test-modal-drag-handle">
                <h2>测试控制面板</h2>
                <button class="modal-close-button" id="close-test-modal">&times;</button>
            </div>

            <div class="tabs" data-modal="test">
                <!-- <button class="tab-button" data-tab="tab-media-frame">媒体框</button> -->
                <button class="tab-button active" data-tab="tab-test-dev">开发者选项</button>
                <button class="tab-button" data-tab="tab-test-vad">语音活动检测</button>
                <!-- <button class="tab-button" data-tab="tab-test4">测试4</button> -->
                <!-- <button class="tab-button" data-tab="tab-test5">测试5</button> -->
                <!-- <button class="tab-button" data-tab="tab-test6">测试6</button> -->
                <!-- <button class="tab-button" data-tab="tab-test7">测试7</button> -->
                <!-- <button class="tab-button" data-tab="tab-test8">测试8</button> -->
                <!-- <button class="tab-button" data-tab="tab-test9">测试9</button> -->
                <!-- <button class="tab-button" data-tab="tab-test10">测试10</button> -->
            </div>

            <div class="modal-scroll-container" data-modal="test">
                <!-- 媒体框测试标签页 -->
                <!-- <div id="tab-media-frame" class="tab-content active">
            </div> -->

                <!-- 测试2标签页 -->
                <div id="tab-test-dev" class="tab-content active">
                    <!-- 显示性能信息——开发者选项 -->
                    <div class="modal-section">
                        <div class="settings-label">显示性能信息——开发者选项</div>
                        <div class="settings-control">
                            <label class="switch">
                                <input type="checkbox" id="showPerformanceStats">
                                <span class="slider"></span>
                            </label>
                            <span class="switch-value">关闭</span>
                        </div>
                        <div class="settings-description">在画面左下角显示FPS和每帧处理时间</div>
                    </div>
                </div>

                <!-- 语音活动检测 -->
                <!-- <div id="tab-test-vad" class="tab-content">
                <div class="modal-section">
                    <div class="section-title">语音活动检测</div>
                    <div id="vad-status" class="status-text">语音监听未启动</div>
                    <div id="vad-waveform-container" class="waveform-container"></div>
                    <div id="vad-status-indicator" class="status-indicator"></div>
                    <button id="vad-toggle-button" class="btn-primary">开启语音监听</button>
                    <div id="vad-settings-container" class="settings-container">
                        <div class="setting-item">
                            <label for="vad-threshold-slider">灵敏度:</label>
                            <div class="range-container">
                                <input type="range" id="vad-threshold-slider" min="0.5" max="0.95" step="0.05" value="0.8">
                                <input type="number" id="vad-threshold-value" min="0.5" max="0.95" step="0.05" value="0.8">
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->

                <!-- 测试3标签页 -->
                <!-- <div id="tab-test3" class="tab-content">
                <div class="modal-section">
                    <div class="section-title">测试3内容</div>
                    <p>这里是测试3的内容</p>
                </div>
            </div> -->
            </div>

        </div>

        <!-- 测试按钮 -->
        <button id="test-button" data-ui="ui"
            style="position: fixed; top: 160px; right: 20px; width: 56px; height: 56px; border-radius: 50%; background-color: var(--primary-color); box-shadow: var(--box-shadow); cursor: pointer; z-index: 1002; display: flex; justify-content: center; align-items: center; border: none;">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path
                    d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z">
                </path>
            </svg>
        </button>

        <!-- 数字人重置按钮 -->
        <button id="reset-dh-button" data-ui="ui"
            style="position: fixed; bottom: 20px; right: 20px; width: 56px; height: 56px; border-radius: 50%; background-color: var(--success-color); box-shadow: var(--box-shadow); cursor: pointer; z-index: 1002; display: flex; justify-content: center; align-items: center; border: none; transition: var(--transition);">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <!-- 修正的四个箭头指向中心的图标 -->
                <polyline points="6 10 10 12 6 14"></polyline>
                <polyline points="18 10 14 12 18 14"></polyline>
                <polyline points="10 6 12 10 14 6"></polyline>
                <polyline points="10 18 12 14 14 18"></polyline>
                <circle cx="12" cy="12" r="1" fill="white" stroke="none"></circle>
            </svg>
            <div class="tooltip tooltip-top">数字人归正</div>
        </button>

        <!-- 显示对话记录的按钮 -->
        <button id="show-chat-modal" data-ui="ui" style="padding: 16px;">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 22" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 16a2 2 0 0 1-2 2H7l-4 4V6a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                <circle cx="7" cy="11" r="1"></circle>
                <circle cx="12" cy="11" r="1"></circle>
                <circle cx="17" cy="11" r="1"></circle>
            </svg>
        </button>

        <!-- &lt;!&ndash; 弹窗遮罩层 &ndash;&gt;-->
        <!-- <div id="modal-overlay"></div>-->

        <!-- 对话记录弹窗 -->
        <div class="chat-modal" id="chatModal" style="width: 400px; height: 300px;">
            <div class="chat-modal-header" id="chatModalHeader">
                <span>灵澈数字人对话</span>
                <button class="modal-close-button" id="close-chat-modal">&times;</button>
            </div>
            <div class="modal-scroll-container" id="chatContainer" style="max-height: 100%">
                <!-- 消息内容 -->
            </div>
            <div class="chat-input-area">
                <textarea id="chat-input" placeholder="输入消息..." rows="1" maxlength="500"></textarea>
                <button type="button" class="btn-primary"
                    style="margin: 0px; display: flex; align-items: center; justify-content: center;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        style="margin-right: 6px; vertical-align: text-bottom;">
                        <line x1="22" y1="2" x2="11" y2="13" />
                        <polygon points="22 2 15 22 11 13 2 9 22 2" />
                    </svg>
                    发送
                </button>
            </div>
        </div>

        <!-- 设置弹窗 -->
        <div id="settings-modal" style="max-width: 80vw; min-width: 350px;">
            <div class="modal-header" id="drag-handle">
                <h2>数字人控制面板</h2>
                <button class="modal-close-button" id="close-modal">&times;</button>
            </div>

            <div class="tabs" data-modal="settings">
                <button class="tab-button active" data-tab="tab-main">基础控制</button>
                <!-- <button class="tab-button" data-tab="tab-knowledge">知识库</button> -->
                <button class="tab-button" data-tab="tab-ui">UI 界面</button>
                <button class="tab-button" data-tab="tab-dh">数字人</button>
                <button class="tab-button" data-tab="tab-chat">对话框</button>
                <button class="tab-button" data-tab="tab-media">数字媒体</button>
                <button class="tab-button" data-tab="tab-recognition">语音识别</button>
                <button class="tab-button" data-tab="tab-music">音乐</button>
                <button class="tab-button" data-tab="tab-knowledge">知识库</button>
            </div>
            <div class="modal-scroll-container" data-modal="settings"
                style="min-height: calc(300px - 140px); max-height: calc(100vh - 140px);">

                <!-- 基础控制标签页 -->
                <div id="tab-main" class="tab-content active">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83" />
                            </svg>
                            数字人控制
                        </div>
                        <div class="switch-container">
                            <label class="switch">
                                <input type="checkbox" id="use-stun">
                                <span class="slider"></span>
                            </label>
                            <label for="use-stun">使用 STUN/TURN 服务器</label>
                        </div>
                        <div class="info-box"
                            style="background-color: #e3f2fd; padding: 12px; border-radius: 8px; margin-top: 16px; margin-bottom: 16px;">
                            <p style="margin: 0; font-size: 14px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 6px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                </svg>
                                提示: 当数字人后端与该页面客户端不在同一局域网时，请开启 STUN/TURN 服务器，再启动数字人。
                            </p>
                        </div>
                        <div class="switch-container" id="use-canvas2d-container">
                            <label class="switch">
                                <input type="checkbox" id="use-canvas2d">
                                <span class="slider"></span>
                            </label>
                            <label for="use-canvas2d">使用 Canvas 2D API</label>
                        </div>
                        <div class="info-box" id="use-canvas2d-info"
                            style="background-color: #e3f2fd; padding: 12px; border-radius: 8px; margin-top: 16px; margin-bottom: 16px;">
                            <p style="margin: 0; font-size: 14px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 6px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                </svg>
                                提示: 如果 WebGL 不可用，将自动降级使用 Canvas 2D API, 但会占用更多 CPU，无法利用 GPU 加速。
                            </p>
                        </div>
                        <div class="button-group">
                            <button id="start" class="btn-success">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                </svg>
                                启动数字人
                            </button>
                            <button id="loading" style="display: none" class="btn-default">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                </svg>
                                加载中...
                            </button>
                            <button id="stop" style="display: none" class="btn-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <rect x="6" y="4" width="4" height="16"></rect>
                                    <rect x="14" y="4" width="4" height="16"></rect>
                                </svg>
                                关闭数字人
                            </button>
                        </div>
                        <input type="hidden" id="sessionid" value="0">
                    </div>

                    <!--<div class="modal-section" style="">
                    <div class="section-title">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83"/>
                        </svg>
                        远控服务
                    </div>
                    <div id="info_div" style="white-space: pre-line; line-height: 1.5; padding: 8px 12px; border-radius: 4px; background-color: #f5f5f5; margin-bottom: 10px; min-height: 24px;">点击按钮开始建立远控链接</div>
                    <div class="button-group">
                        <button id="connect-ws-btn" class="btn-success">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                            建立连接
                        </button>
                        <button id="disconnect-ws-btn" class="btn-warning" style="display: none;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                <rect x="6" y="4" width="4" height="16"></rect>
                                <rect x="14" y="4" width="4" height="16"></rect>
                            </svg>
                            断开连接
                        </button>
                    </div>
                    <input type="hidden" id="sessionid" value="0">
                </div>-->

                    <div class="modal-section" style="display: none;">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                                <line x1="12" y1="19" x2="12" y2="23" />
                                <line x1="8" y1="23" x2="16" y2="23" />
                            </svg>
                            录制控制
                        </div>
                        <div class="button-group">
                            <button class="btn-success" id="btn_start_record">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <circle cx="12" cy="12" r="10" />
                                    <circle cx="12" cy="12" r="3" fill="currentColor" />
                                </svg>
                                开始录制
                            </button>
                            <button class="btn-warning" id="btn_stop_record" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <rect x="6" y="6" width="12" height="12" rx="2" ry="2" />
                                </svg>
                                停止录制
                            </button>
                        </div>
                    </div>


                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                            视觉感知
                        </div>
                        <div class="vision-container">
                            <video id="userCamera" class="vision-camera" autoplay playsinline muted></video>
                            <div class="vision-controls">
                                <button class="vision-btn vision-btn-start" id="startCamera">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M23 7l-7 5 7 5V7z"></path>
                                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                                    </svg>
                                    开启摄像头
                                </button>
                                <button class="vision-btn vision-btn-stop" id="stopCamera" style="display: none;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M23 7l-7 5 7 5V7z"></path>
                                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                                        <line x1="1" y1="1" x2="23" y2="23"></line>
                                    </svg>
                                    关闭摄像头
                                </button>
                            </div>
                            <div id="vision-status" class="vision-status" style="display: none;">

                            </div>
                        </div>
                    </div>


                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                            语音对话
                        </div>
                        <div class="asr-container">
                            <div class="input-box flex-center">
                                <div class="btn-callphone" id="btnButtonCall"></div>
                                <textarea class="input-text" id="message"
                                    onkeydown="if(event.keyCode==13 && !event.shiftKey){event.preventDefault();message_get();return false;}">你是谁？</textarea>
                                <button id="btnSubmit" type="button" class="input-btn"
                                    onclick="message_get()">提交</button>
                                <input type="hidden" id="sessionid" value="0">
                            </div>
                        </div>
                    </div>

                    <!-- <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                            </svg>
                            对话控制
                        </div>
                        <div class="form-group">
                            <label for="audioDelaySlider">对话延迟控制 (秒)</label>
                            <div class="range-container">
                                <input type="range" id="audioDelaySlider" min="0" max="5" value="0" step="0.1">
                                <input type="number" class="range-value" id="audioDelayValue" min="0" max="5.0" value="0.0" step="0.1">
                            </div>
                        </div>
                        <form class="form-inline" id="echo-form">
                            <div class="form-group">
                                <label for="message">输入您想与数字人交流的内容</label>
                                <textarea class="form-control" id="message" rows="3"
                                    placeholder="请输入问题或指令...">你是谁？</textarea>
                            </div>
                            <div style="display: flex; align-items: center; margin-top: 10px; flex-wrap: wrap;">
                                <button type="button" class="btn-primary"
                                    style="margin-right: 15px; height: 36px; display: flex; align-items: center; justify-content: center;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <line x1="22" y1="2" x2="11" y2="13" />
                                        <polygon points="22 2 15 22 11 13 2 9 22 2" />
                                    </svg>
                                    发送
                                </button>

                                控制选项，现在与按钮位于同一行
                                <div style="display: flex; align-items: center; flex-wrap: wrap; height: 36px;">
                                    <div class="switch-container"
                                        style="margin-right: 15px; display: flex; align-items: center; height: 100%;">
                                        <label for="interrupt-toggle"
                                            style="margin-right: 5px; font-size: 13px; color: var(--text-secondary); display: flex; align-items: center;">打断</label>
                                        <label class="switch" style="margin: 0; display: flex; align-items: center;">
                                            <input type="checkbox" id="interrupt-toggle" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>

                                    <div class="switch-container"
                                        style="display: flex; align-items: center; height: 100%;">
                                        <label for="message-type-select"
                                            style="margin-right: 5px; font-size: 13px; color: var(--text-secondary); display: flex; align-items: center;">类型</label>
                                        <select id="message-type-select"
                                            style="padding: 4px; border-radius: 4px; border: 1px solid var(--border-color); background-color: var(--bg-secondary); height: 28px;">
                                            <option value="chat">对话</option>
                                            <option value="echo">朗读</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div> -->

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <!-- 头部（数字人） -->
                                <circle cx="12" cy="8" r="4" />
                                <!-- 脸部AI芯片风格 -->
                                <rect x="10.5" y="6.5" width="3" height="3" rx="1" fill="none" stroke="#4361ee"
                                    stroke-width="1" />
                                <!-- 身体 -->
                                <path d="M6 20v-2a6 6 0 0 1 12 0v2" />
                                <!-- 电子/数字感的点缀 -->
                                <circle cx="5" cy="7" r="1" fill="#4361ee" />
                                <circle cx="19" cy="7" r="1" fill="#4361ee" />
                                <circle cx="12" cy="20" r="1" fill="#4361ee" />
                            </svg>
                            角色设定
                        </div>
                        <div id="basic-control">
                            <!-- 输入框 -->
                            <textarea class="form-control" type="text" id="config-select" placeholder="配置内容"></textarea>
                            <!-- 提交按钮 -->
                            <button style="margin: 10px 10px 10px 0" id="submit-config">提交</button>
                        </div>
                    </div>

                    <!-- <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                            无人演播模式
                        </div>

                        <div class="form-group">
                            <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                <input type="text" id="nb-live-path" class="input-field" style="flex: 1;"
                                    placeholder="请输入文件夹路径">
                                <button id="browse-folder-btn" class="btn-secondary" style="margin: 0;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 5px; vertical-align: text-bottom;">
                                        <path
                                            d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z">
                                        </path>
                                    </svg>
                                    <span class="full-text">选择文件夹</span>
                                    <span class="short-text" style="display: none;">选择</span>
                                </button>
                            </div>
                            <div class="info-box"
                                style="background-color: #fff3e0; padding: 10px; border-radius: 4px; margin-bottom: 15px; border-left: 3px solid #ff9800;">
                                <p style="margin: 0; font-size: 13px; color: #e65100;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="8" x2="12" y2="12"></line>
                                        <line x1="12" y1="16" x2="12" y2="16"></line>
                                    </svg>
                                    <strong>重要提示</strong>：由于浏览器安全限制，选择文件夹后只能获取文件夹名称而非完整路径。请在文件资源管理器中复制完整路径。
                                </p>
                            </div>
                            <button id="submit-nb-path" class="btn-primary" style="width: 100%;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 5px; vertical-align: text-bottom;">
                                    <line x1="22" y1="2" x2="11" y2="13"></line>
                                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                                </svg>
                                提交无人播放文件夹路径
                            </button>
                        </div>
                    </div> -->
                </div>

                <!-- 知识库 -->
                <div id="tab-knowledge" class="tab-content">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20" />
                                <path d="M8 7h6" />
                                <path d="M8 11h8" />
                                <path d="M8 15h5" />
                            </svg>
                            知识数据库连接设置
                        </div>
                        <form id="db-connection-form">
                            <div class="form-group">
                                <label for="db-url">数据库URL</label>
                                <input type="text" id="db-url" class="form-control"
                                    placeholder="例如: mongodb://localhost:27017 或 mysql://localhost:3306">
                            </div>
                            <div class="form-group">
                                <label for="db-name">数据库名称</label>
                                <input type="text" id="db-name" class="form-control" placeholder="输入数据库名称">
                            </div>
                            <div class="form-group">
                                <label for="db-username">用户名</label>
                                <input type="text" id="db-username" class="form-control" placeholder="输入数据库用户名">
                            </div>
                            <div class="form-group">
                                <label for="db-password">密码</label>
                                <input type="password" id="db-password" class="form-control" placeholder="输入数据库密码">
                            </div>
                            <div class="button-group" style="margin-top: 20px">
                                <button type="button" id="test-connection" class="btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <polyline points="20 6 9 17 4 12"></polyline>
                                    </svg>
                                    测试连接
                                </button>
                                <button type="button" id="save-connection" class="btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z">
                                        </path>
                                        <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                        <polyline points="7 3 7 8 15 8"></polyline>
                                    </svg>
                                    保存设置
                                </button>
                                <button type="button" id="submit-connection" class="btn-success">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                    </svg>
                                    提交连接
                                </button>
                            </div>

                            <div class="connection-list-section"
                                style="margin-top: 25px; border-top: 1px solid var(--border-color); padding-top: 15px;">
                                <h4
                                    style="margin: 0 0 10px; font-size: 16px; font-weight: 500; display: flex; align-items: center;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 8px;">
                                        <line x1="8" y1="6" x2="21" y2="6"></line>
                                        <line x1="8" y1="12" x2="21" y2="12"></line>
                                        <line x1="8" y1="18" x2="21" y2="18"></line>
                                        <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                        <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                        <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                    </svg>
                                    连接列表
                                </h4>
                                <div id="connection-list" class="connection-list"
                                    style="max-height: 150px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; padding: 10px;">
                                    <p class="empty-list-message"
                                        style="text-align: center; color: var(--text-secondary);">暂无保存的连接</p>
                                    <!-- 已保存的连接将在这里动态添加 -->
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                                <polyline points="13 2 13 9 20 9"></polyline>
                            </svg>
                            知识库文件上传
                        </div>

                        <div class="file-upload-container"
                            style="border: 2px dashed #ccc; border-radius: 8px; padding: 30px 20px; text-align: center; margin-bottom: 20px; position: relative; background-color: rgba(240, 240, 240, 0.2); display: flex; flex-direction: column; justify-content: center; align-items: center; min-height: 200px;">
                            <input type="file" id="knowledge-file-input" multiple
                                accept=".txt,.md,.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.db,.sql,.sqlite,.mdb"
                                style="position: absolute; width: 100%; height: 100%; top: 0; left: 0; opacity: 0; cursor: pointer;">

                            <div class="upload-icon"
                                style="margin-bottom: 15px; display: flex; justify-content: center; align-items: center; width: 100%;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="color: #4361ee;">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                            </div>

                            <h3
                                style="margin: 0 0 10px; font-size: 18px; font-weight: 600; color: var(--text-primary);">
                                拖拽文件至此处或点击上传</h3>
                            <p style="margin: 0; font-size: 14px; color: var(--text-secondary);">支持的文件格式: .txt, .md,
                                .pdf, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .db, .sql, .sqlite, .mdb</p>
                            <p style="margin-top: 10px; font-size: 12px; color: var(--text-secondary);">单个文件大小不超过50MB
                            </p>
                        </div>

                        <div class="file-list-container" style="margin-top: 20px;">
                            <h4
                                style="margin: 0 0 10px; font-size: 16px; font-weight: 500; display: flex; align-items: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 8px;">
                                    <line x1="8" y1="6" x2="21" y2="6"></line>
                                    <line x1="8" y1="12" x2="21" y2="12"></line>
                                    <line x1="8" y1="18" x2="21" y2="18"></line>
                                    <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                    <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                    <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                </svg>
                                已上传文件
                            </h4>
                            <div id="knowledge-file-list" class="file-list"
                                style="max-height: 200px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 4px; padding: 10px;">
                                <p class="empty-list-message" style="text-align: center; color: var(--text-secondary);">
                                    暂无上传文件</p>
                            </div>
                        </div>

                        <div class="button-group" style="margin-top: 15px;">
                            <button type="button" id="process-files" class="btn-primary" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                </svg>
                                处理文件
                            </button>
                            <button type="button" id="clear-files" class="btn-secondary" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path
                                        d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2">
                                    </path>
                                </svg>
                                清空列表
                            </button>
                        </div>
                    </div>
                </div>

                <!-- UI 设置 -->
                <div id="tab-ui" class="tab-content">
                    <!-- UI显示设置 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <circle cx="12" cy="12" r="6"></circle>
                            </svg>
                            UI显示
                        </div>
                        <div class="form-group">
                            <div class="settings-control">
                                <label class="switch">
                                    <input type="checkbox" id="uiAutoHideSwitch" checked>
                                    <span class="slider"></span>
                                </label>
                                <span class="switch-value" for="uiAutoHideSwitch">启用自动隐藏UI控件</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="uiAutoHideDelay">自动隐藏等待时间（毫秒）</label>
                            <div class="range-container">
                                <input type="range" id="uiAutoHideDelay" min="500" max="15000" value="1500">
                                <input type="number" class="range-value" id="uiAutoHideDelayValue" min="500" max="15000"
                                    value="1500">
                            </div>
                            <div style="font-size:12px;color:#888;margin-top:4px;">范围：0.5秒 ~ 15秒</div>
                        </div>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z" />
                            </svg>
                            背景设置
                        </div>

                        <!-- 背景图片预设 -->
                        <div class="form-group">
                            <label>背景图片预设</label>
                            <div class="bg-presets-container"
                                style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px;">
                                <style>
                                    .bg-presets-row {
                                        display: flex;
                                        margin-bottom: 10px;
                                        gap: 10px;
                                    }

                                    .bg-preset-item {
                                        flex: 1;
                                        position: relative;
                                        padding-top: 28.125%;
                                        /* 16:9比例 = 9/16 * 50% */
                                        border-radius: 4px;
                                        overflow: hidden;
                                        cursor: pointer;
                                        border: 2px solid transparent;
                                        transition: all 0.2s;
                                    }

                                    .bg-preset-item:hover {
                                        border-color: #4361ee;
                                        transform: translateY(-2px);
                                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                                    }

                                    .bg-preset-item.active {
                                        border-color: #4361ee;
                                    }

                                    .bg-preset-item img {
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                    }

                                    .bg-preset-item video {
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                    }
                                </style>

                                <!-- 第一行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-type="video" data-position="cover"
                                        data-src="static\videos/outup.mp4">
                                        <!-- 阻止视频控制器 ，并且隐藏视频控制器 -->
                                        <video src="static\videos/outup.mp4" alt="未来科技横屏1"
                                            style="--webkit-media-controls: none!important;"></video>
                                    </div>
                                    <div class="bg-preset-item" data-type="video" data-position="cover"
                                        data-src="static\videos/outup.mp4">
                                        <video src="static\videos/outup.mp4" alt="未来科技横屏2">
                                    </div>
                                </div>

                                <!-- 第二行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-type="image" data-position="cover"
                                        data-src="static\images/sz-bg3.png">
                                        <img src="static\images/sz-bg3.png" alt="未来科技横屏3">
                                    </div>
                                    <div class="bg-preset-item" data-type="image" data-position="cover"
                                        data-src="static\images/sz-bg4.png">
                                        <img src="static\images/sz-bg4.png" alt="未来科技横屏4">
                                    </div>
                                </div>

                                <!-- 第三行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-type="image" data-position="cover"
                                        data-src="static\images/sz-bg5.png">
                                        <img src="static\images/sz-bg5.png" alt="未来科技竖屏1">
                                    </div>
                                    <div class="bg-preset-item" data-type="image" data-position="cover"
                                        data-src="static\images/sz-bg6.png">
                                        <img src="static\images/sz-bg6.png" alt="未来科技竖屏2">
                                    </div>
                                </div>

                                <!-- 第四行图片 -->
                                <div class="bg-presets-row">
                                    <div class="bg-preset-item" data-type="image" data-position="cover"
                                        data-src="static\images/sz-bg7.png">
                                        <img src="static\images/sz-bg7.png" alt="未来科技竖屏3">
                                    </div>
                                    <div class="bg-preset-item" data-type="image" data-position="cover"
                                        data-src="static\images/sz-bg8.png">
                                        <img src="static\images/sz-bg8.png" alt="未来科技竖屏4">
                                    </div>
                                </div>

                                <!-- 第五行动态 -->
                                <!-- <div class="bg-presets-row">
                                <div class="bg-preset-item" data-bg-url="static\videos/outup.mp4">
                                    <video src="static\videos/outup.mp4" alt="未来科技竖屏3">
                                </div>
                                <div class="bg-preset-item" data-bg-url="static\videos/outup.mp4">
                                    <video src="static\videos/outup.mp4" alt="未来科技竖屏4">
                                </div>
                            </div> -->
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="backgroundUpload">上传背景图片</label>
                            <div class="file-upload">
                                <label for="backgroundUpload" class="file-upload-label">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                                        <circle cx="8.5" cy="8.5" r="1.5" />
                                        <polyline points="21 15 16 10 5 21" />
                                    </svg>
                                    选择图片文件
                                </label>
                                <input type="file" id="backgroundUpload" accept="image/*,video/*,.gif">
                            </div>
                        </div>
                    </div>

                    <!-- 背景位置调整 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                <polyline points="21 15 16 10 5 21"></polyline>
                            </svg>
                            背景位置调整
                        </div>
                        <div class="form-group">
                            <label for="bgXOffsetSlider">背景水平位置</label>
                            <div class="range-container">
                                <input type="range" id="bgXOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="bgXOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="bgYOffsetSlider">背景垂直位置</label>
                            <div class="range-container">
                                <input type="range" id="bgYOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="bgYOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="bgScaleSlider">背景缩放比例</label>
                            <div class="range-container">
                                <input type="range" id="bgScaleSlider" min="50" max="200" value="100">
                                <input type="number" class="range-value" id="bgScaleValue" min="50" max="200"
                                    value="100">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="button-group">
                                <button id="bgFitWidth" class="btn-secondary" style="padding: 8px 12px">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <line x1="3" y1="12" x2="21" y2="12"></line>
                                        <polyline points="3 6 3 18"></polyline>
                                        <polyline points="21 6 21 18"></polyline>
                                    </svg>
                                    长边铺满
                                </button>
                                <button id="bgFitHeight" class="btn-secondary" style="padding: 8px 12px">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <line x1="12" y1="3" x2="12" y2="21"></line>
                                        <polyline points="6 3 18 3"></polyline>
                                        <polyline points="6 21 18 21"></polyline>
                                    </svg>
                                    短边铺满
                                </button>
                                <button id="bgFitFill" class="btn-secondary" style="padding: 8px 12px">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                        <line x1="12" y1="3" x2="12" y2="21"></line>
                                        <rect x="3" y="3" width="18" height="18" rx="1"></rect>
                                        <line x1="12" y1="3" x2="12" y2="21"></line>
                                        <path d="M7 8L12 3L17 8"></path>
                                        <path d="M7 16L12 21L17 16"></path>
                                    </svg>
                                    拉伸填充
                                </button>
                            </div>
                        </div>
                        <div class="info-box"
                            style="background-color: #e3f2fd; padding: 12px; border-radius: 8px; margin-top: 16px;">
                            <p style="margin: 0; font-size: 14px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 6px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                </svg>
                                提示: 您也可以按住Alt键后直接用鼠标拖动背景调整位置以及滑动滚轮调整背景大小。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 数字人标签页 -->
                <div id="tab-dh" class="tab-content">

                    <!-- 数字人绿幕设置 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
                                <polyline points="3.27 6.96 12 12.01 20.73 6.96" />
                                <line x1="12" y1="22.08" x2="12" y2="12" />
                            </svg>
                            绿幕设置
                        </div>
                        <div class="form-group">
                            <label for="bgColorPicker">抠像背景颜色</label>
                            <div class="color-preview">
                                <input type="color" id="bgColorPicker" value="#000000">
                                <input type="text" class="color-value" id="bgColorValue" value="#000000">
                                <!-- 垂直和水平居中 -->
                                <button id="bgColorPickerButton" class="btn-secondary"
                                    style="padding: 8px 12px; margin-left: 10px; margin-bottom: 0">
                                    一键智能抠像
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="toleranceSlider">绿幕识别容差</label>
                            <div class="range-container">
                                <input type="range" id="toleranceSlider" min="0" max="255" value="50">
                                <!--                            <span class="range-value" id="toleranceValue">50</span>-->
                                <input type="number" class="range-value" id="toleranceValue" min="0" max="255"
                                    value="50">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="toleranceSlider">抗锯齿系数</label>
                            <div class="range-container">
                                <!-- 改为分数相当于0.00~1.00，步长0.01 -->
                                <input type="range" id="antialiasingSlider" min="0" max="1" value="0" step="0.01">
                                <input type="number" class="range-value" id="antialiasingValue" min="0" max="1"
                                    value="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                            数字人位置调整
                        </div>
                        <div class="form-group">
                            <div class="button-group"
                                style="display: flex; flex-wrap: nowrap; width: 100%; align-items: stretch;">
                                <button id="positionLeftBottom" class="btn-secondary"
                                    style="flex: 1; white-space: nowrap; min-width: 0; padding: 8px 4px; text-align: center; margin-right: 6px; display: flex; align-items: center; justify-content: center;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 3px;">
                                        <polyline points="4 14 10 14 10 20"></polyline>
                                    </svg>
                                    左下
                                </button>
                                <div
                                    style="display: flex; flex: 2; margin-right: 6px; height: auto; margin-bottom: 8px;">
                                    <button id="positionCenterMiddle" class="btn-secondary"
                                        style="flex: 1; min-width: 0; padding: 8px 4px; text-align: center; border-top-right-radius: 0; border-bottom-right-radius: 0; border-right: none; margin: 0; display: flex; align-items: center; justify-content: center;">
                                        居中
                                    </button>
                                    <button id="positionCenterBottom" class="btn-secondary"
                                        style="flex: 1; min-width: 0; padding: 8px 4px; text-align: center; border-top-left-radius: 0; border-bottom-left-radius: 0; margin: 0; display: flex; align-items: center; justify-content: center;">
                                        底部
                                    </button>
                                </div>
                                <button id="positionRightBottom" class="btn-secondary"
                                    style="flex: 1; white-space: nowrap; min-width: 0; padding: 8px 4px; text-align: center; display: flex; align-items: center; justify-content: center;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="margin-right: 3px;">
                                        <polyline points="20 14 14 14 14 20"></polyline>
                                    </svg>
                                    右下
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="xOffsetSlider">水平位置 (X轴)</label>
                            <div class="range-container">
                                <input type="range" id="xOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="xOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="yOffsetSlider">垂直位置 (Y轴)</label>
                            <div class="range-container">
                                <input type="range" id="yOffsetSlider" min="-1900" max="1900" value="0">
                                <input type="number" class="range-value" id="yOffsetValue" min="-1900" max="1900"
                                    value="0">
                            </div>
                        </div>
                        <div class="info-box"
                            style="background-color: #e3f2fd; padding: 12px; border-radius: 8px; margin-top: 16px; margin-bottom: 20px;">
                            <p style="margin: 0; font-size: 14px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 6px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                </svg>
                                提示: 您也可以按住空格键后直接用鼠标拖动数字人调整位置以及滑动滚轮调整大小。
                            </p>
                        </div>

                        <div class="form-group" style="display: none;">
                            <label for="digitalHumanWidthSlider">随媒体框运动后数字人缩放至的宽度</label>
                            <div class="range-container">
                                <input type="range" id="digitalHumanWidthSlider" min="35" max="650" value="300">
                                <input type="number" class="range-value" id="digitalHumanWidthValue" min="35" max="650"
                                    value="300">
                                <div style="margin-top: 8px; display: flex; justify-content: flex-end;">
                                    <button id="setCurrentWidthButton" class="btn-secondary"
                                        style="padding: 6px 10px; font-size: 12px;">
                                        使用当前宽度
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 将替换到原代码的尺寸设置部分 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                            </svg>
                            数字人大小设置
                        </div>
                        <div class="form-group">
                            <div style="display: flex; justify-content: space-between;">
                                <div class="switch-container" style="margin-right: 10px;">
                                    <label class="switch">
                                        <input type="checkbox" id="originRatioLockCheckbox">
                                        <span class="slider"></span>
                                    </label>
                                    <label for="originRatioLockCheckbox">锁定原始比例</label>
                                </div>
                                <div class="switch-container">
                                    <label class="switch">
                                        <input type="checkbox" id="ratioLockCheckbox">
                                        <span class="slider"></span>
                                    </label>
                                    <label for="ratioLockCheckbox">锁定当前比例</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="customWidthSlider">数字人宽度</label>
                            <div class="range-container">
                                <input type="range" id="customWidthSlider" min="100" max="1900" value="500">
                                <input type="number" class="range-value" id="customWidthValue" min="100" max="1900"
                                    value="500">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="customHeightSlider">数字人高度</label>
                            <div class="range-container">
                                <input type="range" id="customHeightSlider" min="100" max="1900" value="900">
                                <input type="number" class="range-value" id="customHeightValue" min="100" max="1900"
                                    value="900">
                            </div>
                        </div>
                    </div>

                </div>

                <!-- 对话框设置标签页 -->
                <div id="tab-chat" class="tab-content">
                    <div class="section-title">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                            <circle cx="7" cy="10" r="1" />
                            <circle cx="12" cy="10" r="1" />
                            <circle cx="17" cy="10" r="1" />
                        </svg>
                        对话框设置
                    </div>
                    <div class="modal-section">
                        <div class="section-subtitle">
                            整体设置
                        </div>
                        <!-- 对话框主题颜色设置 -->
                        <div class="form-group">
                            <label>主题颜色</label>
                            <div class="btn-group" id="theme-buttons">
                                <button class="btn" style="background-color: #4A90E2" data-theme="light">浅色主题</button>
                                <button class="btn" style="background-color: #0d1115" data-theme="dark">深色主题</button>
                                <button class="btn" style="background-color: #4A90E2" data-theme="blue">蓝色主题</button>
                                <button class="btn" style="background-color: #4caf50" data-theme="green">绿色主题</button>
                                <button class="btn" style="background-color: #9B59B6" data-theme="reset">重置</button>
                            </div>
                        </div>

                        <!-- 对话框透明度设置 -->
                        <div class="form-group">
                            <label for="modalOpacitySlider">对话框透明度</label>
                            <div class="range-container">
                                <input type="range" id="modalOpacitySlider" min="0" max="0.95" step="0.1" value="0.95">
                                <input type="number" class="range-value" id="modalOpacityValue" min="0" max="0.95"
                                    step="0.1" value="0.95">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>消息背景透明度</label>
                            <div class="slider-group">
                                <input type="range" id="messageOpacitySlider" min="0" max="0.95" step="0.1"
                                    value="0.95">
                                <input type="number" class="range-value" id="messageOpacityValue" min="0" max="0.95"
                                    step="0.1" value="0.95">
                            </div>
                        </div>

                        <!-- 字体设置 -->
                        <div class="form-group">
                            <label for="fontSizeSlider">字体大小</label>
                            <div class="range-container">
                                <input type="range" id="fontSizeSlider" min="10" max="16" value="14">
                                <input type="number" class="range-value" id="fontSizeValue" min="10" max="16"
                                    value="14">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>字体</label>
                            <select class="font-select" id="fontFamily">
                                <option value="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif">系统默认
                                </option>
                                <option value="Arial, sans-serif">Arial</option>
                                <option value="'Times New Roman', serif">Times New Roman</option>
                                <option value="'Courier New', monospace">Courier New</option>
                                <option value="'Microsoft YaHei', sans-serif">微软雅黑</option>
                                <option value="'SimSun', serif">宋体</option>
                            </select>
                        </div>

                        <!-- 隐藏标题栏 -->
                        <div class="form-group">
                            <label for="hideHeaderSwitch">隐藏标题栏</label>
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="hideHeaderSwitch">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="info-box"
                                style="background-color: #e3f2fd; padding: 12px; border-radius: 8px; margin-top: 16px; margin-bottom: 16px;">
                                <p style="margin: 0; font-size: 14px; color: #0d47a1;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 6px;">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                    </svg>
                                    提示: 关闭时，只能通过标题栏拖动对话框。
                                </p>
                            </div>
                            <label for="hideChatAreaSwitch">隐藏对话栏</label>
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="hideChatAreaSwitch">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                    </div>

                    <!-- 新增对话框位置调整部分 -->
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                            </svg>
                            对话框位置调整
                        </div>
                        <div class="form-group">
                            <div class="button-group"
                                style="display: flex; flex-wrap: nowrap; width: 100%; align-items: stretch;">
                                <button id="chatPosLeftCenter" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px; margin-right: 6px;">
                                    左侧
                                </button>
                                <button id="chatPosCenterBottom" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px; margin-right: 6px;">
                                    底部
                                </button>
                                <button id="chatPosRightCenter" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px;">
                                    右侧
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="button-group"
                                style="display: flex; flex-wrap: nowrap; width: 100%; align-items: stretch;">
                                <button id="chatPosLeftBottom" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px; margin-right: 6px;">
                                    左下角
                                </button>
                                <button id="chatPosRightBottom" class="btn-secondary"
                                    style="flex: 1; padding: 8px 4px;">
                                    右下角
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="chatXPositionSlider">对话框水平位置</label>
                            <div class="range-container">
                                <input type="range" id="chatXPositionSlider" min="0" max="4000" value="50">
                                <input type="number" class="range-value" id="chatXPositionValue" min="0" max="4000"
                                    value="50">
                                <!-- <span class="unit">%</span> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="chatYPositionSlider">对话框垂直位置</label>
                            <div class="range-container">
                                <input type="range" id="chatYPositionSlider" min="0" max="2000" value="80">
                                <input type="number" class="range-value" id="chatYPositionValue" min="0" max="2000"
                                    value="80">
                                <!-- <span class="unit">%</span> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="chatWidthSlider">对话框宽度</label>
                            <div class="range-container">
                                <input type="range" id="chatWidthSlider" min="300" max="1000" value="400">
                                <input type="number" class="range-value" id="chatWidthValue" min="300" max="1000"
                                    value="400">
                                <!-- <span class="unit">%</span> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="chatHeightSlider">对话框高度</label>
                            <div class="range-container">
                                <input type="range" id="chatHeightSlider" min="240" max="1000" value="300">
                                <input type="number" class="range-value" id="chatHeightValue" min="240" max="1000"
                                    value="300">
                                <!-- <span class="unit">%</span> -->
                            </div>
                        </div>
                    </div>

                    <div class="modal-section" style="display: none;">
                        <div class="section-subtitle">
                            聊天框背景
                        </div>
                        <div class="form-group">
                            <label>背景颜色</label>
                            <div class="color-picker-group">
                                <div class="color-btn" style="background: rgba(255,255,255,0.95)" data-target="modal-bg"
                                    data-color="rgba(255,255,255,0.95)"></div>
                                <div class="color-btn" style="background: rgba(86, 134, 109, 0.95)"
                                    data-target="modal-bg" data-color="rgba(93, 145, 118, 0.95)"></div>
                                <div class="color-btn" style="background: rgba(33,37,41,0.95)" data-target="modal-bg"
                                    data-color="rgba(33,37,41,0.95)"></div>
                                <div class="color-btn" style="background: rgba(90, 140, 199, 0.95)"
                                    data-target="modal-bg" data-color="rgba(101, 159, 224, 0.95)"></div>
                                <div class="color-btn" style="background: rgba(198, 110, 110, 0.95)"
                                    data-target="modal-bg" data-color="rgba(255, 141, 141, 0.95)"></div>
                            </div>
                        </div>
                        <!-- <div class="form-group">
                        <label for="containerOpacitySlider">背景透明度</label>
                        <div class="slider-group">
                            <input type="range" id="containerOpacitySlider" min="0" max="0.95" step="0.1" value="0.95">
                            <input type="number" class="range-value" id="containerOpacityValue" min="0" max="0.95" step="0.1" value="0.95">
                        </div>
                    </div> -->
                    </div>

                    <div class="modal-section" style="display: none;">
                        <div class="section-subtitle">
                            用户消息背景
                        </div>
                        <div class="form-group">
                            <label>背景颜色</label>
                            <div class="color-picker-group">
                                <div class="color-btn" style="background: rgba(240,240,240,0.9)" data-target="user-bg"
                                    data-color="rgba(240,240,240,0.9)"></div>
                                <div class="color-btn" style="background: rgba(248,249,250,0.9)" data-target="user-bg"
                                    data-color="rgba(248,249,250,0.9)"></div>
                                <div class="color-btn" style="background: rgba(230,230,230,0.9)" data-target="user-bg"
                                    data-color="rgba(230,230,230,0.9)"></div>
                                <div class="color-btn" style="background: rgba(224,247,250,0.9)" data-target="user-bg"
                                    data-color="rgba(224,247,250,0.9)"></div>
                                <div class="color-btn" style="background: rgba(255,248,225,0.9)" data-target="user-bg"
                                    data-color="rgba(255,248,225,0.9)"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>文字颜色</label>
                            <div class="color-picker-group">
                                <div class="color-btn" style="background: #333" data-target="user-color"
                                    data-color="#333"></div>
                                <div class="color-btn" style="background: #666" data-target="user-color"
                                    data-color="#666"></div>
                                <div class="color-btn" style="background: #000" data-target="user-color"
                                    data-color="#000"></div>
                                <div class="color-btn" style="background: #4A90E2" data-target="user-color"
                                    data-color="#4A90E2"></div>
                                <div class="color-btn" style="background: #FF6B6B" data-target="user-color"
                                    data-color="#FF6B6B"></div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-section" style="display: none;">
                        <div class="section-subtitle">
                            系统消息背景
                        </div>
                        <div class="form-group">
                            <label>背景颜色</label>
                            <div class="color-picker-group">
                                <div class="color-btn" style="background: rgba(240,240,240,0.9)" data-target="system-bg"
                                    data-color="rgba(240,240,240,0.9)"></div>
                                <div class="color-btn" style="background: rgba(248,249,250,0.9)" data-target="system-bg"
                                    data-color="rgba(248,249,250,0.9)"></div>
                                <div class="color-btn" style="background: rgba(230,230,230,0.9)" data-target="system-bg"
                                    data-color="rgba(230,230,230,0.9)"></div>
                                <div class="color-btn" style="background: rgba(224,247,250,0.9)" data-target="system-bg"
                                    data-color="rgba(224,247,250,0.9)"></div>
                                <div class="color-btn" style="background: rgba(255,248,225,0.9)" data-target="system-bg"
                                    data-color="rgba(255,248,225,0.9)"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>文字颜色</label>
                            <div class="color-picker-group">
                                <div class="color-btn" style="background: #333" data-target="system-color"
                                    data-color="#333"></div>
                                <div class="color-btn" style="background: #666" data-target="system-color"
                                    data-color="#666"></div>
                                <div class="color-btn" style="background: #000" data-target="system-color"
                                    data-color="#000"></div>
                                <div class="color-btn" style="background: #4A90E2" data-target="system-color"
                                    data-color="#4A90E2"></div>
                                <div class="color-btn" style="background: #FF6B6B" data-target="system-color"
                                    data-color="#FF6B6B"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 外观设置标签页 -->
                <div id="tab-media" class="tab-content">

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                            媒体框控制
                        </div>

                        <div class="form-group" style="margin-top: 16px;">
                            <label for="mediaTechPositionMode">播放媒体时定位模式</label>
                            <select id="mediaTechPositionMode" class="form-control" style="margin-bottom: 15px;">
                                <option value="0" selected>随机左右</option>
                                <option value="1">始终左侧</option>
                                <option value="2">始终右侧</option>
                                <option value="3">始终顶部</option>
                                <!-- 小屏时隐藏该项 -->
                                <option value="4" id="mediaTechCenterPositionMode">始终中心</option>
                                <option value="5">始终底部</option>
                                <option value="6">左右交替</option>
                            </select>
                        </div>

                        <div class="button-group">
                            <button id="toggle-media-frame" class="control-button"
                                style="position: static; width: auto; height: auto; padding: 10px 20px; margin-bottom: 10px; display: inline-flex; align-items: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 8px;">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                </svg>
                                打开媒体框
                            </button>
                            <button id="close-media-frame" class="control-button"
                                style="position: static; width: auto; height: auto; padding: 10px 20px; margin-bottom: 10px; display: inline-flex; align-items: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 8px;">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                </svg>
                                仅关闭媒体框
                            </button>
                        </div>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z">
                                </path>
                                <path
                                    d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z">
                                </path>
                            </svg>
                            动画效果
                        </div>
                        <div class="button-group" style="display: flex; flex-wrap: wrap; gap: 8px;">
                            <button class="animation-button active" data-animation="scale">缩放动画</button>
                            <button class="animation-button" data-animation="rotate">旋转动画</button>
                            <button class="animation-button" data-animation="slide">滑动动画</button>
                            <button class="animation-button" data-animation="flip">翻转动画</button>
                            <button class="animation-button" data-animation="bounce">弹性动画</button>
                            <button class="animation-button" data-animation="fade">淡入动画</button>
                        </div>
                    </div>
                </div>

                <!-- 语音识别标签页 -->
                <div id="tab-recognition" class="tab-content">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                            语音识别 (ASR)
                        </div>
                        <div class="asr-container">
                            <div class="recoder_mode_div" style="display: none;">
                                <div class="asr-radio-group">
                                    <div class="asr-radio-option">
                                        <input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio"
                                            value="mic" checked id="mic-radio">
                                        <label for="mic-radio">麦克风</label>
                                    </div>
                                    <div class="asr-radio-option">
                                        <input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio"
                                            value="file" id="file-radio">
                                        <label for="file-radio">文件</label>
                                    </div>
                                </div>
                            </div>

                            <!-- 结果区域 -->
                            <div class="control-section">
                                <h4 class="asr-title" style="margin-top: 0px;">语音识别结果</h4>
                                <textarea id="asrVarArea" readonly placeholder="识别结果将显示在这里..."></textarea>
                            </div>

                            <!-- 状态提示 -->
                            <div id="asr_info_div"
                                style="margin-bottom: 16px; font-weight: 500; color: var(--text-primary); text-align: center; padding: 8px; border-radius: 8px; background-color: rgba(67, 97, 238, 0.1);">
                                点击开始按钮启动语音识别</div>

                            <!-- 按钮区域 -->
                            <div class="button-group"
                                style="display: flex; justify-content: center; gap: 5px; margin-bottom: 16px;">
                                <button id="asr-btn-connect" class="asr-button"
                                    style="background-color: var(--primary-color);">连接</button>
                                <button id="asr-btn-start" class="asr-button"
                                    style="background-color: var(--success-color);">开启</button>
                                <button id="asr-btn-stop" class="asr-button"
                                    style="background-color: var(--warning-color);" disabled>关闭</button>
                            </div>

                            <!-- 音频播放器 -->
                            <audio id="asr-audio-record" type="audio/wav" controls
                                style="display: none; width: 100%; height: 40px; border-radius: 8px; margin-top: 8px;"></audio>

                            <!-- 音量相关控件 -->
                            <div class="volume-controls" style="margin-top: 15px;">
                            </div>

                            <!-- 分割线 -->

                            <!-- 高级设置区域 -->
                            <div id="asr-advanced-settings" style="display: none;">
                                <!-- ASR模型选择 -->
                                <div id="asr-mic-mode-div" class="control-section"
                                    style="margin-bottom: 16px; margin-right: 26px;">
                                    <h4
                                        style="margin: 0 0 12px 0; color: var(--text-primary); font-weight: 600; font-size: 15px;">
                                        选择ASR模型模式</h4>
                                    <div class="radio-group"
                                        style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 16px;">
                                        <div class="radio-option"
                                            style="display: flex; align-items: center; cursor: pointer;">
                                            <input name="asr_mode" type="radio" value="2pass" id="asr-2pass-radio"
                                                checked style="margin-right: 8px;">
                                            <label for="asr-2pass-radio"
                                                style="font-size: 14px; color: var(--text-primary); cursor: pointer;">2pass</label>
                                        </div>
                                        <div class="radio-option"
                                            style="display: flex; align-items: center; cursor: pointer;">
                                            <input name="asr_mode" type="radio" value="online" id="asr-online-radio"
                                                style="margin-right: 8px;">
                                            <label for="asr-online-radio"
                                                style="font-size: 14px; color: var(--text-primary); cursor: pointer;">Online</label>
                                        </div>
                                        <div class="radio-option"
                                            style="display: flex; align-items: center; cursor: pointer;">
                                            <input name="asr_mode" type="radio" value="offline" id="asr-offline-radio"
                                                style="margin-right: 8px;">
                                            <label for="asr-offline-radio"
                                                style="font-size: 14px; color: var(--text-primary); cursor: pointer;">Offline</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 文件上传 -->
                                <div id="asr-rec-mode-div" class="control-section"
                                    style="display: none; margin-bottom: 16px; margin-right: 26px;">
                                    <h4
                                        style="margin: 0 0 12px 0; color: var(--text-primary); font-weight: 600; font-size: 15px;">
                                        选择音频文件</h4>
                                    <div class="asr-file-upload">
                                        <label for="asr-upfile" class="asr-file-upload-label">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                style="margin-right: 8px; vertical-align: text-bottom;">
                                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                                <polyline points="17 8 12 3 7 8" />
                                                <line x1="12" y1="3" x2="12" y2="15" />
                                            </svg>
                                            选择音频文件
                                        </label>
                                        <input type="file" id="asr-upfile" accept="audio/*">
                                    </div>
                                </div>

                                <!-- ITN选项 -->
                                <div id="asr-use-itn-div" class="control-section"
                                    style="margin-bottom: 16px; margin-right: 26px;">
                                    <h4
                                        style="margin: 0 0 12px 0; color: var(--text-primary); font-weight: 600; font-size: 15px;">
                                        反向文本归一化 (ITN)</h4>
                                    <div class="asr-radio-group">
                                        <div class="asr-radio-option">
                                            <input name="use_itn" type="radio" value="false" id="asr-itn-false" checked>
                                            <label for="asr-itn-false">否</label>
                                        </div>
                                        <div class="asr-radio-option">
                                            <input name="use_itn" type="radio" value="true" id="asr-itn-true">
                                            <label for="asr-itn-true">是</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 热词 -->
                                <div class="control-section"
                                    style="display: none; margin-bottom: 16px; margin-right: 26px;">
                                    <h4
                                        style="margin: 0 0 12px 0; color: var(--text-primary); font-weight: 600; font-size: 15px;">
                                        热词设置</h4>
                                    <p style="margin: 0 0 8px 0; color: var(--text-secondary); font-size: 13px;">
                                        每行一个关键词，空格分隔权重，例如 "阿里巴巴 20"</p>
                                    <textarea id="asr-var-hot" rows="3">阿里巴巴 20 hello world 40</textarea>
                                </div>
                            </div>

                            <!-- 显示/隐藏高级设置按钮 -->
                            <button id="asr-toggle-advanced" class="asr-button"
                                style="width: 100%; background-color: var(--background-dark); color: var(--text-primary);">
                                显示高级设置
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom;">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">语音活动检测</div>
                        <div class="control-section">
                            <div class="form-group">
                                <label style="font-size: 13px; color: var(--text-secondary);">活动状态检测：<span
                                        id="vad-status">语音监听未启动</span></label>
                                <button id="vad-toggle-button" class="btn-primary" style="width: 100%;">
                                    开启语音活动检测
                                </button>
                            </div>

                            <div class="setting-item">
                                <label for="vad-threshold-slider">灵敏度:</label>
                                <div class="range-container">
                                    <input type="range" id="vad-threshold-slider" min="0.5" max="0.95" step="0.05"
                                        value="0.8">
                                    <input type="number" id="vad-threshold-value" min="0.5" max="0.95" step="0.05"
                                        value="0.8">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                            指定唤醒词
                        </div>

                        <div id="wake_info_div"
                            style="margin-bottom: 16px; font-weight: 500; color: var(--text-primary); text-align: center; padding: 8px; border-radius: 8px; background-color: rgba(67, 97, 238, 0.1);">
                            请说出唤醒词，唤醒数字人</div>

                        <!-- 唤醒词检测开关 -->
                        <div class="switch-container" style="justify-content: space-between; margin-bottom: 20px;">
                            <label for="wakeup-detection-toggle"
                                style="margin: 0; font-size: 14px; color: var(--text-secondary);">启用人唤醒词检测（必须先说出一次唤醒词，才会开始响应）</label>
                            <label class="switch">
                                <input type="checkbox" id="wakeup-detection-toggle" checked>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <!-- 唤醒超时时间滑块 -->
                        <div class="form-group">
                            <label for="wakeup-timeout" style="font-size: 13px; color: var(--text-secondary);">唤醒超时时间
                                (毫秒)</label>
                            <div class="slider-group">
                                <input type="range" id="wakeup-timeout" class="slider" min="500" max="600000" step="500"
                                    value="180000">
                                <input type="number" id="wakeup-timeout-value" class="range-value" min="500"
                                    max="600000" step="500" value="180000">
                            </div>
                        </div>

                        <div class="control-section">
                            <textarea class="form-control" id="wakeupWord" rows="3">小幸运</textarea>
                            <button id="wakeupWordBtn" class="btn-secondary" style="width: 100%; margin-top: 10px;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 6px; vertical-align: text-bottom;">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                </svg>
                                保存
                            </button>
                        </div>
                    </div>

                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                </path>
                            </svg>
                            人脸检测
                        </div>

                        <!-- 人脸检测摄像头（移动到主体中，初始隐藏但不会自动隐藏） -->
                        <div class="switch-container" style="justify-content: space-between; margin-bottom: 20px;">
                            <label for="face-detection-toggle"
                                style="margin: 0; font-size: 14px; color: var(--text-secondary);">启用人脸检测（数字人被唤醒前需验证人脸）</label>
                            <label class="switch">
                                <input type="checkbox" id="face-detection-toggle" checked>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <!-- 高级设置区域 -->
                        <div id="face-advanced-settings"
                            style="margin-top: 15px; border-top: 1px solid var(--border-color); padding-top: 15px; display: none;">
                            <div class="setting-group-title"
                                style="margin-bottom: 10px; font-weight: 500; color: var(--text-primary);">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path
                                        d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z">
                                    </path>
                                </svg>
                                高级设置
                            </div>

                            <!-- 身份验证开关 -->
                            <div class="switch-container" style="justify-content: space-between; margin-bottom: 15px;">
                                <label for="face-verification-toggle"
                                    style="margin: 0; font-size: 13px; color: var(--text-secondary);">需要身份验证</label>
                                <label class="switch small-switch">
                                    <input type="checkbox" id="face-verification-toggle">
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <!-- 检测间隔滑块 -->
                            <!-- 使用和之前一样的 form-group 加一个label加slider-group再加两个input的形式 -->
                            <div class="form-group">
                                <label for="face-detection-interval"
                                    style="font-size: 13px; color: var(--text-secondary);">检测间隔 (毫秒)</label>
                                <div class="slider-group">
                                    <input type="range" id="face-detection-interval" class="slider" min="50" max="1000"
                                        step="50" value="100">
                                    <input type="number" id="face-detection-interval-value" class="range-value" min="50"
                                        max="1000" step="50" value="100">
                                </div>
                            </div>

                            <!-- 验证阈值滑块 -->
                            <!-- 使用和之前一样的 form-group 加一个label加slider-group再加两个input的形式 -->
                            <div class="form-group">
                                <label for="face-verification-threshold"
                                    style="font-size: 13px; color: var(--text-secondary);">验证精度 (越小越严格)</label>
                                <div class="slider-group">
                                    <input type="range" id="face-verification-threshold" class="slider" min="0.1"
                                        max="0.9" step="0.05" value="0.6">
                                    <input type="number" id="face-verification-threshold-value" class="range-value"
                                        min="0.1" max="0.9" step="0.05" value="0.6">
                                </div>
                            </div>

                            <!-- 人脸验证超时时间 -->
                            <div class="form-group">
                                <label for="face-verification-timeout"
                                    style="font-size: 13px; color: var(--text-secondary);">验证超时时间 (毫秒)</label>
                                <div class="slider-group">
                                    <input type="range" id="face-verification-timeout" class="slider" min="1000"
                                        max="60000" step="500" value="5000">
                                    <input type="number" id="face-verification-timeout-value" class="range-value"
                                        min="1000" max="60000" step="500" value="5000">
                                </div>
                            </div>

                            <!-- 清空人脸数据按钮 -->
                            <button id="face-clear-profiles-btn" class="btn-secondary"
                                style="width: 100%; margin-top: 10px;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="margin-right: 5px; vertical-align: text-bottom;">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path
                                        d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2">
                                    </path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                                清除所有人脸数据
                            </button>
                        </div>

                        <div class="info-box"
                            style="background-color: #e3f2fd; padding: 10px; border-radius: 4px; margin-top: 15px; border-left: 3px solid #2196f3; display: none;">
                            <p style="margin: 0; font-size: 13px; color: #0d47a1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="16"></line>
                                    <line x1="12" y1="16" x2="12" y2="16"></line>
                                </svg>
                                开启此功能后，发送语音识别结果前系统会自动打开摄像头进行人脸验证，只有检测到人脸后才会发送结果
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 音乐播放器标签页 -->
                <div id="tab-music" class="tab-content">
                    <div class="modal-section">
                        <div class="section-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M9 18V5l12-2v13"></path>
                                <circle cx="6" cy="18" r="3"></circle>
                                <circle cx="18" cy="16" r="3"></circle>
                            </svg>
                            本地音乐播放器
                        </div>

                        <!-- 上传音乐文件和播放列表 -->
                        <div class="music-player-container" style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <!-- 文件选择和列表展示 -->
                            <div class="file-input-section" style="flex: 1; min-width: 250px;">
                                <div class="file-upload" style="margin-bottom: 10px;">
                                    <label for="music-file-input" class="file-upload-label">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="17 8 12 3 7 8"></polyline>
                                            <line x1="12" y1="3" x2="12" y2="15"></line>
                                        </svg>
                                        选择音乐文件 ( 可多选 )
                                    </label>
                                    <input type="file" id="music-file-input" accept="audio/*" multiple>
                                </div>

                                <div class="playlist-container" style="margin-bottom: 15px;">
                                    <div style="font-weight: 500; margin-bottom: 5px;">播放列表</div>
                                    <!-- 新的播放列表UI，使用div和ul/li代替select/option -->
                                    <div class="custom-playlist-wrapper"
                                        style="border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden; max-height: 200px; position: relative;">
                                        <!-- 滚动容器 -->
                                        <div class="custom-playlist-scroll"
                                            style="overflow-y: auto; max-height: 200px; padding: 5px 0;">
                                            <!-- 实际播放列表 -->
                                            <ul id="music-playlist-ul" class="custom-playlist"
                                                style="list-style: none; margin: 0; padding: 0;">
                                                <!-- 音乐项目将在这里动态生成 -->
                                                <li class="playlist-empty-message"
                                                    style="padding: 10px; text-align: center; color: var(--text-secondary);">
                                                    播放列表为空</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <!-- 隐藏的原始select元素，用于兼容现有JS代码 -->
                                    <select id="music-playlist" style="display: none;" hidden>
                                        <!-- 音乐列表会在这里动态生成 -->
                                    </select>
                                    <!-- 当前播放歌曲信息 -->
                                    <div id="now-playing-info"
                                        style="margin: 10px 0; font-size: 13px; color: var(--text-secondary); white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                        未播放任何音乐
                                    </div>
                                    <div class="playlist-controls"
                                        style="display: flex; justify-content: center; gap: 8px; margin-top: 8px;">
                                        <button class="btn-small playlist-control-btn" id="play-selected"
                                            style="flex: 1; background-color: #3a56d4;">
                                            <span class="btn-text-normal">播放选中</span>
                                            <span class="btn-text-short">播放</span>
                                        </button>
                                        <button class="btn-small playlist-control-btn" id="remove-selected"
                                            style="flex: 1; background-color: #e18709;">
                                            <span class="btn-text-normal">移除选中</span>
                                            <span class="btn-text-short">移除</span>
                                        </button>
                                        <button class="btn-small playlist-control-btn" id="clear-playlist"
                                            style="flex: 1; background-color: #d5081c;">
                                            <span class="btn-text-normal">清空列表</span>
                                            <span class="btn-text-short">清空</span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 播放器控制区 -->
                            <div class="player-controls-section" style="flex: 1; min-width: 250px;">

                                <!-- 播放进度条 -->
                                <div class="progress-container" style="margin-bottom: 15px;">
                                    <div
                                        style="display: flex; justify-content: space-between; font-size: 12px; margin-bottom: 3px;">
                                        <span id="current-time">00:00</span>
                                        <span id="total-time">00:00</span>
                                    </div>
                                    <div class="progress-bar-container"
                                        style="position: relative; height: 6px; background: #e0e0e0; border-radius: 3px;">
                                        <div id="music-progress-bar"
                                            style="position: absolute; height: 100%; background: var(--primary-color); width: 0; border-radius: 3px;">
                                        </div>
                                        <input type="range" id="music-progress-slider"
                                            style="position: absolute; width: 100%; height: 100%; opacity: 0; cursor: pointer;"
                                            min="0" max="100" value="0">
                                    </div>
                                </div>

                                <!-- 音量控制 - 移到进度条下方 -->
                                <div class="volume-control"
                                    style="display: flex; align-items: center; gap: 8px; margin-bottom: 15px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                                        <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                                        <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                                    </svg>
                                    <input type="range" id="volume-slider" min="0" max="100" value="80"
                                        style="flex-grow: 1;">
                                    <span id="volume-value">80%</span>
                                </div>

                                <!-- 播放控制按钮 - 重新排列布局 -->
                                <div class="playback-controls"
                                    style="display: flex; justify-content: center; align-items: center; gap: 15px; margin-bottom: 15px;">
                                    <button id="prev-track" class="circle-button"
                                        style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: var(--primary-color); border: none; color: white; padding: 0;">
                                        <!-- 图标将通过JS动态设置 -->
                                    </button>
                                    <button id="play-pause" class="circle-button"
                                        style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: var(--primary-color); border: none; color: white; padding: 0;">
                                        <!-- 图标将通过JS动态设置 -->
                                    </button>
                                    <button id="next-track" class="circle-button"
                                        style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: var(--primary-color); border: none; color: white; padding: 0;">
                                        <!-- 图标将通过JS动态设置 -->
                                    </button>
                                </div>

                                <!-- 播放模式设置 -->
                                <div class="playback-settings">
                                    <div class="setting-item" style="margin-bottom: 10px;">
                                        <label for="loop-mode">循环模式:</label>
                                        <div class="custom-select-wrapper"
                                            style="position: relative; display: inline-block; width: calc(100% - 80px);">
                                            <select id="loop-mode"
                                                style="appearance: none; -webkit-appearance: none; -moz-appearance: none; width: 100%; border-radius: 4px; border: 1px solid var(--border-color); padding: 8px 30px 8px 10px; background-color: var(--bg-color); cursor: pointer;">
                                                <option value="list">列表循环</option>
                                                <option value="single">单曲循环</option>
                                                <option value="no-loop">不循环</option>
                                                <option value="random">随机播放</option>
                                            </select>
                                            <div class="select-arrow"
                                                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="6 9 12 15 18 9"></polyline>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="setting-item"
                                        style="margin-bottom: 10px; display: flex; align-items: center;">
                                        <label for="loop-count" style="margin-right: 10px;">循环次数:</label>
                                        <input type="number" id="loop-count" min="0" max="100" value="1"
                                            style="width: 60px; border-radius: 4px; border: 1px solid var(--border-color); padding: 5px;">
                                        <span
                                            style="margin-left: 5px; color: var(--text-secondary); font-size: 12px;">(0表示无限循环)</span>
                                    </div>

                                    <div class="setting-item"
                                        style="margin-bottom: 10px; display: flex; align-items: center;">
                                        <label for="loop-interval" style="margin-right: 10px;">循环间隔(秒):</label>
                                        <input type="number" id="loop-interval" min="0" max="60" value="2"
                                            style="width: 60px; border-radius: 4px; border: 1px solid var(--border-color); padding: 5px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 隐藏的音频元素用于实际播放 -->
                        <audio id="music-player" style="display:none;"></audio>
                    </div>
                </div>

            </div>
        </div>

        <!-- 成功提示弹窗 -->
        <div id="success-toast"
            style="display: none; position: fixed; top: 30px; left: 50%; transform: translateX(-50%); background-color: #4caf50; color: white; padding: 15px 20px; border-radius: 5px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); z-index: 1000; opacity: 0; transition: opacity 0.3s ease-in-out;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span id="toast-message">提交成功!</span>
            </div>
        </div>
    </div>

    <script src="./vendors/jquery-2.1.1.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./vendors/pinyin-pro.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./vendors/recorder/recorder-core.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/fix/recorder-config.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/fix/recorder-initialize.js" type="text/javascript" charset="utf-8"></script>
    <script src="./vendors/recorder/wav.js" type="text/javascript" charset="utf-8"></script>
    <script src="./vendors/recorder/pcm.js" type="text/javascript" charset="utf-8"></script>
    <script src="./vendors/face-api/face-api.js" type="text/javascript" charset="utf-8"></script>
    <!-- VAD 语音活动检测所需的库 -->
    <script src="https://cdn.jsdelivr.net/npm/onnxruntime-web@1.19.2/dist/ort.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@ricky0123/vad-web@0.0.19/dist/bundle.min.js"></script>


    <!-- <script src="./vendors/vad/ort.js"></script>
    <script src="./vendors/vad/bundle.min.js"></script> -->


    <script type="module" src="./config.js"></script>
    <script type="module" src="./main.js"></script>

    <script type="text/javascript" src="./zdy/wsconnecter.js"></script>
    <script type="text/javascript" src="./zdy/utils.js"></script>
    <script type="text/javascript" src="./zdy/camera-vision.js"></script>



    <!-- 人脸注册对话框 -->
    <div id="face-register-dialog" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        style="vertical-align: middle; margin-right: 8px;">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    人脸注册
                </h3>
                <button class="close-btn" id="face-register-close">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="display: flex; flex-direction: column; align-items: center; gap: 15px;">
                    <div id="face-register-preview"
                        style="position: relative; width: 320px; height: 240px; background-color: #333; border-radius: 8px; overflow: hidden;">
                        <video id="face-register-video" width="320" height="240" autoplay muted
                            style="transform: scaleX(-1);"></video>
                        <canvas id="face-register-canvas" width="320" height="240"
                            style="position: absolute; top: 0; left: 0; transform: scaleX(-1);"></canvas>
                        <div id="face-register-status"
                            style="position: absolute; bottom: 10px; left: 10px; color: white; font-size: 14px; background: rgba(0,0,0,0.5); padding: 5px 10px; border-radius: 4px;">
                            准备注册...</div>
                    </div>
                    <div style="width: 100%;">
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="face-profile-name"
                                style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500;">人脸配置文件名称</label>
                            <input type="text" id="face-profile-name" class="input-field" placeholder="请输入名称 (如: 个人配置)"
                                style="width: 100%;">
                        </div>
                        <div class="info-box"
                            style="background-color: #fff3e0; padding: 10px; border-radius: 4px; margin-bottom: 15px; border-left: 3px solid #ff9800;">
                            <p style="margin: 0; font-size: 13px; color: #e65100;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="12"></line>
                                    <line x1="12" y1="16" x2="12" y2="16"></line>
                                </svg>
                                请确保光线充足，面部清晰可见。注册过程中请保持面部在摄像头中。
                            </p>
                        </div>
                        <div style="display: flex; justify-content: space-between; gap: 10px;">
                            <button id="face-register-cancel" class="btn-secondary" style="flex: 1;">
                                取消
                            </button>
                            <button id="face-register-confirm" class="btn-primary" style="flex: 1;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" style="vertical-align: text-bottom; margin-right: 5px;">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                </svg>
                                确认注册
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>

</html>