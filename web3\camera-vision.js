// 摄像头和视觉识别功能
let userStream = null;
let userCamera, startCameraBtn, stopCameraBtn; //, visionResult, visionText;

// 初始化DOM元素引用
function initializeCameraElements() {
  userCamera = document.getElementById("userCamera");
  startCameraBtn = document.getElementById("startCamera");
  stopCameraBtn = document.getElementById("stopCamera");
  // visionResult = document.getElementById("visionResult");
  // visionText = document.getElementById("visionText");
}

// 开启摄像头
async function startCamera() {
  try {
    userStream = await navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: 640 },
        height: { ideal: 480 },
        facingMode: "user",
      },
      audio: false,
    });

    userCamera.srcObject = userStream;
    startCameraBtn.style.display = "none";
    stopCameraBtn.style.display = "inline-block";

    console.log("摄像头已开启");
  } catch (error) {
    console.error("无法访问摄像头:", error);
    alert("无法访问摄像头，请确保您已授予摄像头权限！");
  }
}

// 关闭摄像头
function stopCamera() {
  if (userStream) {
    userStream.getTracks().forEach((track) => track.stop());
    userStream = null;
    userCamera.srcObject = null;

    startCameraBtn.style.display = "inline-block";
    stopCameraBtn.style.display = "none";
    // visionResult.style.display = "none";

    console.log("摄像头已关闭");
  }
}



// 初始化摄像头功能
function initializeCameraFeatures() {
  // 初始化DOM元素引用
  initializeCameraElements();

  // 绑定事件
  startCameraBtn.addEventListener("click", startCamera);
  stopCameraBtn.addEventListener("click", stopCamera);

  // 自动开启摄像头
  // startCamera();
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", function () {
  initializeCameraFeatures();
});

// 页面卸载时关闭摄像头
window.addEventListener("beforeunload", function () {
  stopCamera();
});


